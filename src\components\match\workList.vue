<template>
    <!-- 内容区域 -->
    <div class="workCon" ref="scrollContainer" :style="scrollBehaviorStyle" :class="{'instant-scroll': isInstantScroll}">
        <div class="my-works" v-if="myList.id">
            <div class="my-works-title">我的作品</div>
            <WorkItem v-if="myList.id" :data="myList" type="my" @item-click="videoCardClick" />
        </div>
        <div class="other-works" v-if="list.length">
            <div class="other-works-title">其他作品</div>
            <!-- 视频卡片网格 -->
            <div v-if="list.length" class="masonry-wrapper">
                <div v-masonry="masonryId" transition-duration="0" item-selector=".item">
                    <div v-masonry-tile v-for="(item, index) in list" :key="index">
                        <WorkItem :data="item" type="other" @item-click="videoCardClick" />
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div v-if="isShowDetail">
        <workDetail :data="data" :type="dataDetailType" :isShowDetail="isShowDetail" @closePopup="closePopup"></workDetail>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed, onActivated, inject, defineAsyncComponent } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import _ from 'lodash';
import emitter from '@/utils/mitt';
import WorkItem from './workItem.vue';
import { getMatchWorks, getMyMatchWorks } from '@/api/match';
import { showToast } from 'vant';

const workDetail = defineAsyncComponent(() =>
    import('@/components/match/workDetail.vue')
);
const Empty = defineAsyncComponent(() =>
    import('@/components/common/empty.vue')
);
// 接收父组件传递的props
const props = defineProps({
    matchId: {
        type: [String, Number],
        required: true
    }
});
const myList = ref({});
// 定义要向父组件发出的事件
const emit = defineEmits(['scroll-position-saved']);

const $redrawVueMasonry = inject('redrawVueMasonry');
const router = useRouter();
const route = useRoute();
const isShowDetail = ref(false);
const data = ref({});
const dataDetailType = ref('');
// 滚动容器引用
const scrollContainer = ref(null);
const loading=ref(false)
const finished=ref(false)
// 生成唯一的瀑布流标识符
const generateUniqueId = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `work-masonry-${timestamp}-${random}`;
};

// 为当前组件实例创建一个唯一的瀑布流标识符
const masonryId = ref(generateUniqueId());
console.log('生成workList瀑布流标识符:', masonryId.value);

// 数据状态
const list = ref([]);
const total = ref(0);
const pageNum = ref(1);
const pageSize = ref(10);
// 用于跟踪当前请求的页码，解决请求竞态问题
const currentRequestPageNum = ref(1);

// 存储滚动位置
const savedScrollPosition = ref(0);

// 添加状态变量，用于记录是否从视频页面返回
const isFromVideo = ref(false);

// 控制滚动行为
const isInstantScroll = ref(false);
const scrollBehaviorStyle = computed(() => {
    return isInstantScroll.value
        ? { scrollBehavior: 'auto' }
        : { scrollBehavior: 'smooth' };
});

// 添加布局状态变量
const isLayoutReady = ref(false);
const layoutAttempts = ref(0);
const MAX_LAYOUT_ATTEMPTS = 3;

const getMyMatchWorksData = async () => {
    try {
        const params = {
            contestId: props.matchId,
        }
        const res = await getMyMatchWorks(params);
        if (res.code === 200 && res) {
            myList.value = res.data;
            console.log('我的参赛作品:', res);
        } else if (res.code == 500) {
            console.log('暂无数据');
        } else {
            console.log('获取我的参赛作品失败');
        }
    } catch (error) {
        console.error('获取我的参赛作品异常:', error);
    }
}
// 获取赛事详情参赛作品
const getMatchWorksData = async () => {
    try {
        // 记录发起请求时的页码
        const requestPageNum = pageNum.value;
        currentRequestPageNum.value = requestPageNum;

        const params = {
            contestId: props.matchId,
            pageNum: pageNum.value,
            pageSize: pageSize.value
        }
        const res = await getMatchWorks(params);
        console.log('赛事详情参赛作品:', res);

        // 检查当前响应对应的请求页码是否与发送请求时的页码一致
        // 如果不一致，说明存在更新的请求，应该忽略当前响应
        // 但是对于页码为1的请求，始终接受响应，因为这是基础数据
        if (requestPageNum !== currentRequestPageNum.value && requestPageNum !== 1) {
            console.log(`忽略过期的响应结果，请求页码: ${requestPageNum}，当前页码: ${currentRequestPageNum.value}`);
            return;
        }

        if (res.code === 200 && res) {
            if (requestPageNum === 1) {
                // 使用请求时的页码判断，而不是当前的pageNum.value
                list.value = res.rows;
                console.log('设置第1页数据，共', res.rows.length, '条');
            } else {
                list.value = [...list.value, ...res.rows];
                console.log('添加第', requestPageNum, '页数据，共', res.rows.length, '条');
            }
            total.value = res.total;

            // 检查是否已加载完所有数据
            if (list.value.length >= total.value) {
                finished.value = true;
                console.log('已加载完所有数据');
            }

            // 重新布局瀑布流
            nextTick(() => {
                relayoutMasonry(100);
            });
        } else {
            console.log('获取参赛作品失败');
        }
    } catch (error) {
        console.error('获取参赛作品异常:', error);
    } finally {
        // 结束loading状态
        loading.value = false;
    }
};

// 改进的瀑布流布局函数
const relayoutMasonry = (delay = 300) => {
    // 重置布局状态
    isLayoutReady.value = false;
    layoutAttempts.value = 0;
    
    // 预布局 - 立即触发一次布局以确保基本结构
    if (typeof $redrawVueMasonry === 'function') {
        // 创建并分发一个自定义事件，而不是使用全局resize事件
        if (scrollContainer.value) {
            const customEvent = new CustomEvent('work-masonry-resize', {
                detail: { masonryId: masonryId.value }
            });
            scrollContainer.value.dispatchEvent(customEvent);
        }
        
        $redrawVueMasonry(masonryId.value);
        // console.log(`瀑布流[${masonryId.value}]预布局完成`);
    }
    
    // 主布局 - 延迟执行以确保DOM已更新
    setTimeout(() => {
        // 先触发自定义事件，但仅针对当前组件
        if (scrollContainer.value) {
            const customEvent = new CustomEvent('work-masonry-resize', {
                detail: { masonryId: masonryId.value }
            });
            scrollContainer.value.dispatchEvent(customEvent);
        }

        // 然后使用vue-masonry的重绘方法，但仅针对当前瀑布流实例
        if (typeof $redrawVueMasonry === 'function') {
            $redrawVueMasonry(masonryId.value);
        }

        // console.log(`瀑布流[${masonryId.value}]主布局完成`);
        
        // 设置布局就绪状态
        isLayoutReady.value = true;
        
        // 如果是从视频返回且有保存的滚动位置，尝试恢复
        if (isFromVideo.value && savedScrollPosition.value > 0) {
            restoreScrollPositionOptimized();
        }
    }, delay);
    
    // 后续布局 - 确保图片加载后布局正确
    setTimeout(() => {
        tryAdditionalLayout();
    }, delay + 500);
};

// 移除了handleMasonryResize函数

// 尝试额外布局，确保所有内容正确显示
const tryAdditionalLayout = () => {
    if (layoutAttempts.value >= MAX_LAYOUT_ATTEMPTS) return;
    
    layoutAttempts.value++;
    // console.log(`尝试额外布局 #${layoutAttempts.value} 对瀑布流[${masonryId.value}]`);
    
    if (typeof $redrawVueMasonry === 'function') {
        // 使用自定义事件而不是全局resize事件
        if (scrollContainer.value) {
            const customEvent = new CustomEvent('work-masonry-resize', {
                detail: { masonryId: masonryId.value }
            });
            scrollContainer.value.dispatchEvent(customEvent);
        }
        
        $redrawVueMasonry(masonryId.value);
    }
    
    // 如果还需要额外布局，继续尝试
    if (layoutAttempts.value < MAX_LAYOUT_ATTEMPTS) {
        setTimeout(tryAdditionalLayout, 500);
    }
};

// 瀑布流加载更多数据
const changeWaterfallData = _.debounce((params) => {
    console.log('加载更多数据');
    if (list.value.length < total.value) {
        console.log(total.value);
        const oldPageNum = pageNum.value;
        pageNum.value++;
        console.log(`页码更新: ${oldPageNum} -> ${pageNum.value}`);
        getMatchWorksData();
    }
}, 1000, { leading: true, trailing: false })

// 外部滚动容器引用
const externalScrollContainer = ref(null);

// 改进的加载更多数据函数
const loadMoreData = _.debounce(() => {
    console.log('触发加载更多数据');
    if (list.value.length < total.value && !loading.value) {
        console.log('当前数据量:', list.value.length, '总数据量:', total.value);
        loading.value = true;
        const oldPageNum = pageNum.value;
        pageNum.value++;
        console.log(`页码更新: ${oldPageNum} -> ${pageNum.value}`);
        getMatchWorksData();
    } else if (list.value.length >= total.value) {
        finished.value = true;
        console.log('已加载完所有数据');
    }
}, 200);

// 滚动事件处理函数
const handleScroll = _.throttle(() => {
    if (!externalScrollContainer.value || loading.value || finished.value) {
        return;
    }

    const container = externalScrollContainer.value;
    const scrollTop = container.scrollTop;
    const scrollHeight = container.scrollHeight;
    const clientHeight = container.clientHeight;

    // 距离底部300px时触发加载
    const threshold = 300;
    const distanceToBottom = scrollHeight - scrollTop - clientHeight;

    if (distanceToBottom <= threshold) {
        console.log('触发滚动加载，距离底部:', distanceToBottom);
        loadMoreData();
    }
}, 100);

// 查找外部滚动容器
const findExternalScrollContainer = () => {
    // 查找match-detail-container-content元素
    const container = document.querySelector('.match-detail-container-content');
    if (container) {
        externalScrollContainer.value = container;
        console.log('找到外部滚动容器:', container);
        return true;
    }
    return false;
};

// 添加滚动监听
const addScrollListener = () => {
    if (findExternalScrollContainer()) {
        externalScrollContainer.value.addEventListener('scroll', handleScroll, { passive: true });
        console.log('已添加外部滚动监听');
    } else {
        console.warn('未找到外部滚动容器');
    }
};

// 移除滚动监听
const removeScrollListener = () => {
    if (externalScrollContainer.value) {
        externalScrollContainer.value.removeEventListener('scroll', handleScroll);
        console.log('已移除外部滚动监听');
    }
};

// 视频卡片点击
const videoCardClick = (item, type) => {
    console.log(item, type);
    // 判断是否是视频如果是视频进入视频页面
    if (item.voteType == '2') {
        // 移除了滚动位置保存相关代码
        const data = {
            contestId:item.contestId,
            id: item.id,
            type: 1,
            fromMatch: true,// 标记是从match页面进入的
            userId: item.userIdStr,
            isMatch: true
        }
        router.push({ path: '/video', query: data })
    } else {
        // 打开弹框详情页面前，先将当前项目数据赋值给data对象
        data.value = item;
        dataDetailType.value = type
        console.log('设置详情数据:', data.value);
        isShowDetail.value = true;
    }

};
const closePopup = () => {
    isShowDetail.value = false;
}

// 移除了滚动位置恢复相关函数

// 提供给父组件的方法
defineExpose({
    relayoutMasonry
});

// 在组件挂载后初始化
onMounted(() => {
    // 移除了自定义事件监听相关代码
    
    // 初始化瀑布流布局
    relayoutMasonry();

    // 添加窗口大小变化监听，但使用防抖函数避免频繁触发
    const handleResize = _.debounce(() => {
        relayoutMasonry();
    }, 200);
    window.addEventListener('resize', handleResize);

    // 监听homeBack事件，处理从视频页面返回的情况
    emitter.on('homeBackMatch', () => {
        console.log('workList组件接收到homeBackMatch事件');
        // 移除了滚动位置恢复相关代码，只重新布局
        relayoutMasonry(50);
    });

    // 获取我的参赛作品
    getMyMatchWorksData();
    // 获取赛事参赛作品
    getMatchWorksData();

    // 延迟添加滚动监听，确保DOM已渲染
    setTimeout(() => {
        addScrollListener();
    }, 500);
    
    // 保存清理函数，以便在组件卸载时调用
    onUnmounted(() => {
        window.removeEventListener('resize', handleResize);

        // 移除外部滚动监听
        removeScrollListener();

        // 移除homeBack事件监听
        emitter.off('homeBackMatch');
        emitter.off('refurbishWorkMyList');
        emitter.off('refurbishWorkOtherList');
    });
});

onActivated(() => {
    console.log('matchList组件激活');
    
    // 重新生成瀑布流标识符，确保唯一性
    masonryId.value = generateUniqueId();
    console.log('重新生成workList瀑布流标识符:', masonryId.value);
    
    // 设置布局未就绪状态，准备重新布局
    isLayoutReady.value = false;
    
    // 检查localStorage标记，判断是否从视频页面返回
    const fromVideoToMatch = localStorage.getItem('fromVideoToMatch');
    console.log('localStorage标记：fromVideoToMatch=', fromVideoToMatch);
    console.log('isFromVideo:', isFromVideo.value, 'route.query.fromMatch:', route.query.fromMatch);

    // 如果是从视频页面返回（通过事件、路由参数或localStorage标记判断），不重新加载数据
    if (isFromVideo.value || route.query.fromMatch || fromVideoToMatch === 'true') {
        console.log('从视频页面返回，不重新加载数据');

        // 如果是通过localStorage标记判断的，清除标记
        if (fromVideoToMatch === 'true') {
            localStorage.removeItem('fromVideoToMatch');
            console.log('清除localStorage标记：fromVideoToMatch');
            
            // 设置状态变量，标记是从视频页面返回
            isFromVideo.value = true;
        }
        
        // 立即触发布局，布局完成后会自动恢复滚动位置（如果有）
        relayoutMasonry(50);
    } else {
        console.log('非视频页面返回，重新加载第一页数据');
        // 重新加载第一页数据
        pageNum.value = 1;
        currentRequestPageNum.value = 1; // 同时重置请求页码
        console.log('重置页码：', pageNum.value, '当前请求页码：', currentRequestPageNum.value);
        getMatchWorksData();
    }

    // 重置状态变量，确保下次激活时状态正确
    // 注意：我们在处理完当前激活后才重置，这样不会影响当前的判断逻辑
    setTimeout(() => {
        if (isFromVideo.value) {
            isFromVideo.value = false;
            console.log('重置isFromVideo为false');
        }
    }, 500);
});

emitter.on('refurbishWorkMyList', () => {
    console.log('workList组件接收到refurbishWorkMyList事件');
    // pageNum.value = 1;
    // currentRequestPageNum.value = 1; // 同时重置请求页码
    // list.value = [];
    // getMatchWorksData();
    getMyMatchWorksData()
})
emitter.on('refurbishWorkOtherList', () => {
    console.log('workList组件接收到refurbishWorkOtherList事件');
    pageNum.value = 1;
    currentRequestPageNum.value = 1; // 同时重置请求页码
    list.value = [];
    getMatchWorksData();
})
</script>

<style lang="scss" scoped>
// 内容区域样式
.workCon {
    height: 100%;
    padding: 0 78px;
    // overflow: auto;
    display: flex;
    flex-direction: column;

    .my-works {
        .my-works-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 20px 0;
            text-align: center;
        }
    }

    .other-works {
        flex: 1;

        .other-works-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 20px 0;
            text-align: center;
        }
    }
}

// 瀑布流容器
.vanList {
    width: 100%;
    height: 100%;
    // padding-bottom: 40px; // 底部添加一些padding，防止内容被遮挡
}

.masonry-wrapper {
    width: 100%;
    // padding-top: 10px; // 顶部添加一些padding
    min-height: 100%; // 确保最小高度为100%
    contain: layout style paint; // 优化渲染性能
}

.noComment {
    width: 100%;
    height: 100%;
}

// 确保v-masonry容器正确显示
[v-masonry] {
    width: 100% !important;
    margin: 0 auto;
    transform: translateZ(0); // 强制启用硬件加速
    backface-visibility: hidden; // 防止渲染闪烁
    perspective: 1000; // 改善3D性能
    will-change: transform; // 提示浏览器优化变换
    contain: layout style paint; // 优化渲染性能
}

// 加载指示器样式
.loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    color: #666;
}

// 没有更多数据提示样式
.no-more-data {
    text-align: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
}
</style>
