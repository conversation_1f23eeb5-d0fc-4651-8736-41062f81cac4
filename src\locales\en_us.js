
const en_us = {
    index:{
        title:'Post home page',
        menuItems: {
            Dynamic: 'Posts',
            Favorites: 'Collect',
            Liked: 'Liked',
            Messages: 'Message',
            weChat:'Moments',
            myDynamic:'My posts',
            myFavorites:'My favorites',
            myLiked:'My liked',
            Hot:'Hot',
            Recommend:'Recommend',
            New:'Latest',
            Vote:'Guess & Gain',
        },
        user:{
            praised:'<PERSON>ain likes',
            collect:'Collect',
            chat:'Chat',
            addFriends:'Add friends',
            friendRequest:'Friend Request',
            verificationMessage:'Verification Message',
            sendNow:'Send Now',
            verificationMessagePlaceholder:'How are you! Nice to meet you'
        },
        msg:{
            likeCollectTab:'Like and favorite',
            commentAitTab:"Comments and{'@'}",
            reply:'Reply',
            likeCond:'I liked your post',
            likeConc:'Liked your comment',
            likeConr:'Liked your reply',
            collectCon:"I've saved your post",
            commentCon:'Commented on your post',
            replyCon:'Replied',
            aitCon:"{'@'}you"
        }
    },
    detail:{
      replyNum:'A total of %num% replies',
      seeMore:'See more',
      stop:'Fold up',
      originalText:'Original text',
      translation:'Translate',
      commentNum:'comments',
      send:'Send ',
      reply:'Reply',
      replied:'Replied',
      likeListTitle:'Likes from %num% friends'
    },
    add:{
       title:'Post',
       input:'Please enter Post content',
       topic:'Add topic',
       whoSee:{
          title:'Who can I show it to',
          all:'Everyone',
          friend:'Friends can see',
          oneself:'Only visible to yourself',
       },
       publish:'Release',
       audioStart:'Click to speak',
       audioEnd:'Click to end',
       searchPlaceholder:'Search for your friends',
       confirm:'Confirm',
       audioPermission:'Please open the microphone permission first',
       imagePermission:'Please enable camera permissions first',
       aitUser:"{'@'}user"
    },
    report:{
       next:'Next step',
       confirmReport:'Confirm the report',
       placeholder:'Please enter content',
       detailTitle:'Detailed description',
       imgTitle:'Image evidence',
       reportSuccess:'Report successful',
       reportFail:'Report failed',
       reportSuccessInfo:'After submission, the platform will actively verify and process; thank you for your effort to maintain the social environment!',
       reportPublish:'Return dynamics',
       reportOneTitleAfterD:'You are reporting the post of %name%',
       reportOneTitleAfterC:'You are reporting comment of %name%',
       reportOneTitleAfterR:'You are reporting reply of %name%',
       reportTwoTitle:'You have selected %title%',
       reportEndTitleAfterD:'Your report for post of %name% belongs to  %title%',
       reportEndTitleAfterC:'Your report for comments of %name% belongs to   %title%',
       reportEndTitleAfterR:'Your report for replies of %name% belongs to  %title%',
    },
    tooltip:{
        delete:'Delete',
        modify:'Modify',
        cancelCollect:'Unfavorite',
        report:'Report',
        block:'Block User',
    },
    delete:{
      deleteCon:'Are you sure you want to delete this content?',
      deleteCancel:'Cancel',
      deleteConfirm:'Confirm',
      blockCon:'Are you sure you want to block?',
    },
    toast:{
        likeSuccess:'Like successful',
        likeCancel:'Like has been canceled',
        likeFail:'Like failed',
        collectSuccess:'Favorite successful',
        collectCancel:'Unfavorited',
        collectFail:'Favorite failed',
        publishSuccess:'Post successfully',
        publishFail:'Post failed',
        modifySuccess:'Modification successful',
        topicInfo:'You can choose up to 5 topics',
        aitInfo:"Up to {'@'}5 users",
        ait:"Please enter at least 1 character before @",
        dynamicInput:'Please upload at least one of the following: dynamic content, images, or videos',
        nextInfo:'Please select one option first',
        reportSuccess:'Report successful',
        reportFail:'Report failed',
        audioTextSuccess:'Voice to text successful',
        audioTextFail:'Voice to text failed',
        translationSuccess:'Translation successful',
        translationFail:'Translation failed',
        uploadImageFail:'Upload failed',
        deleteSuccess:'Delete successful',
        deleteFail:'Deletion failed',
        // 新加
        imageLimit:'The file size cannot exceed %num%MB.',
        imageNum:'Up to 9 images can be uploaded',
        uploadPrompt:'Please click to upload images & videos',
        filePrompt:'(Doc, docx and PDF file formats are supported, and the file size cannot exceed 5MB)',
        imageBefore:'No more than 4MB for a single image',
        imageShowToast:'Uploaded file is too large',
        audioFail:'Error ending recording',
        collectCancelFail:'Cancellation failed',
        collectCancelSuccess:'Cancellation successful',
        dynamicFail:'Post does not exist',
        addCommentViolation:'The content you submitted is suspected of violating regulations, please modify and resubmit. Modify and resubmit.',
        addCommentFail:'Failed to add comment',
        addReplyFail:'Failed to add reply',
        addDynamicViolation:'The "post" content you submitted is suspected of violating regulations, please modify it and resubmit.',
        addTopicViolation:'The content of the "topic" you submitted is suspected of violating regulations, please modify it and resubmit.',
        addImageViolation:'The "image" content you submitted is suspected of violating regulations, please modify it and resubmit.',
        topicCon:'The topic content cannot be empty',
        getMsgFail:'Failed to retrieve information.',
        loginFail:'Login failed',
        aitInfoPermission:'Currently visible only to yourself',
        alreadyReport:'You have reported multiple times, please wait for platform feedback',
        commentAfterDelete:'Comments have been deleted',
        replyAfterDelete:'The reply has been deleted',
        msgDataListFail:'Data acquisition failed',
        videoLimit:'The maximum video is no more than 25M',
        videoPrompt:'Upload up to 1 video',
        videoToast:'Only pictures or videos can be uploaded',
        imageTitle:'Upload photo',
        videoTitle:'Upload the video',
        applySuccess:'Friend Request Sent Successfully',
        applyFail:'Friend Request Failed to Send',
        blacklistPrompt:'Cannot Add Friend as User is in Blacklist',
        friendNumPrompt:"The User's Friend List is Full",
        myNumPrompt:'Your Friend List is Full',
        failedPrompt:'Parameter Error',
        alignPrompt:'Already Added as Friend, Cannot Request Again',
        applyMyPrompt:'Cannot Add Yourself',
        alignApply:"You've already sent a friend request. You can send another one after 48 hours",
        blockSuccess:'The user has been added to the blacklist',
        blockFail:'Block failed',
        blockListFull:'The block list is full',
        checkAgreementPrompt:"You don't agree with《Content Posting Agreement》, so you can't publish the news",
        AgreementFile:'You have read and agreed to the document',
        fileTitle:'《Content Posting Agreement》',
        sameLanguagePrompt:'Currently in the same language, no translation needed',

    },
    vote:{
        voteProgress:'In Progress',
        voteEnd:'Ended',
        voteSettle:'Settled',
        oneselfNum:'Voted',
        voteNum:'{num} Coin',
        timeName:'Remaining Time',
        allNum:'Total Coins',
        participateInVoting:'Total Players:',
        getCoins:'This time, you have earned {num} coins',
        voteBtn:'Choose',
        voteTitle:'Vote for {num}',
        inputInfo:'Please select the quantity',
        voteConfirm:'Confirm',
        voteSuccess:'Success',
        voteFail:'Failure',
        statusEnd:'The event has ended',
        voteTnfo:'The minimum number of coins required to participate in the event is 1',
        hold:'Hold',
        balance:'Your current account balance is insufficient. Please recharge promptly',
        remainingTimeData:'{days} days {hours} hours {minutes} mins {seconds} secs',
        questionInfo:"All of the user's coins will go into the prize pool for this event, and users who guess correctly will divide all the 【Time-space coins】 in the prize pool according to the number of coins they have guessed.",
    },
    video:{
        videoIndex:'Say something...',
        videoDetail:'Detail Page',
        videoTitle:'Video',
    },
    empty:{
        comment:'No comments yet',
        list:'No data available',
        content:'No content available',
        message:'No messages available'
    }
}

export default en_us;



