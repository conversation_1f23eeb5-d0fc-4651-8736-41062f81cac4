<template>
    <div class="noCommentCon">
        <div class="empty" :class="data">{{ title }}</div>
    </div>
</template>
<script setup>

const props = defineProps({
    title: {
        type: String,
        default: '暂无评论'
    },
    data: {
        type: String,
        default: ''
    }
})

</script>
<style lang="scss" scoped>
.noCommentCon {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;

    .empty {
        text-align: center;
        background: url('@/assets/images/index/noComment.png') no-repeat;
        background-size: 100% 100%;
        width: 522px;
        height: 168px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--bearTtileColor);
        font-size: var(--size_24);
    }

    .commentEmpty {
        background: url('@/assets/images/detail/noComment.png') no-repeat;
        background-size: 100% 100%;
        width: 302px;
        height: 168px;

    }
}
</style>