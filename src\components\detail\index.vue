<template>
    <div>
        <van-popup v-model:show="dialogVisible" round @click.stop="onClickPopup" @click-overlay.stop="onClickOverlay"
            :close-on-click-overlay="false" teleport="body" :overlay-style="{ zIndex: 2000 }" :z-index="2001">
            <div class="container">
                <div class="leftCon" v-if="detailData.id">
                    <div class="top">
                        <div class="material">
                            <avatar :url="detailData.avatart" :userId="detailData.userId"></avatar>
                            <div class="info">
                                <div class="name">{{ detailData.userName }}</div>
                                <div class="time">{{ time(data.createTime) }}</div>
                            </div>
                        </div>
                        <div class="rightBtn">
                            <translation v-if="detailData.content.length > 0" :content="detailData.content"
                                :language="detailData.language" @changeTranslationCon="changeTranslationCon">
                            </translation>
                            <div class="more">
                                <popover :data="detailData" :tooltipType="tooltipType" :type="type"
                                    @closeDetail="closeDetail"></popover>
                            </div>

                        </div>
                    </div>
                    <div class="centerCon">
                        <div class="topic" v-if="detailData.tagList.length">
                            <span v-for="(item, index) in detailData.tagList" :key="index">#{{ item }}</span>
                        </div>
                        <div class="topic" v-if="detailData.aitList.length">
                            <span v-for="(item, index) in detailData.aitList" :key="index">@{{ item.name }}</span>
                        </div>
                        <div class="msg">
                            {{ translationCon }}
                        </div>
                        <div class="seeFile" v-if="detailData.attachmentUrl" @click="openFile">
                            <img src="@/assets/images/index/listFile.png" />
                            <div class="name">{{ detailData.attachmentName }}</div>
                            <div class="size">{{ detailData.attachmentSize }}</div>
                        </div>
                        <!-- 轮播图 -->
                        <!-- {{ detailData.imageList.length }} -->
                        <Swiper v-if="detailData.imageList.length" :data="detailData.imageList" />
                        <!-- <videoPlay v-if="detailData.video" :data="detailData.video" :imageData="detailData.videoFrame">
                        </videoPlay> -->
                    </div>
                    <!-- like -->
                    <div class="like" v-if="detailData.likeList.length" @click="seeMoreLike">
                        <div class="likeCon">
                            <img src="@/assets/images/index/likeList.png" />
                            <div class="likeList">
                                <template v-for="(item, index) in detailData.likeList" :key="index">
                                    <span class="like-item" :class="item.trim() == '' ? 'showSpace' : ''">
                                        <span class="item-text" dir="auto">{{ item }}</span>
                                    </span>
                                    <span v-if="index !== detailData.likeList.length - 1" class="separator">、</span>
                                </template>
                            </div>
                        </div>
                        <img src="@/assets/images/detail/seeMore.png" />
                    </div>
                    <!-- 底部标签 -->
                    <div class="bottomBtn">
                        <div class="bottomItem rightBorder">
                            <img :src="imgLoveUrl" @click.stop="changeLike(detailData.id)" />

                            <div>{{ detailData.likeNum }}</div>
                        </div>
                        <div class="bottomItem rightBorder">
                            <img src="@/assets/images/detail/comment.png" />
                            <div>{{ detailData.commentNum }}</div>
                        </div>
                        <div class="bottomItem">
                            <img :src="imgCollectUrl" @click.stop="changeCollect(detailData.id)" />
                            <div>{{ detailData.collectNum }}</div>
                        </div>
                    </div>
                </div>
                <div class="rightCon">
                    <!-- 评论 -->
                    <div class="comment">
                        <div class="title">{{ total }} {{ $t('detail.commentNum') }}</div>
                        <div v-if="commentData.length" class="commentCon">
                            <div v-for="(item, index) in commentData" :key="index">
                                <CommentItem :data="item" @getFocus="getFocus">
                                </CommentItem>
                            </div>
                            <div v-if="!loading && commentData.length < total" class="spreadMore" @click="spreadMore">
                                <span>{{ $t('detail.seeMore') }}</span>
                                <img src="@/assets/images/detail/spread.png" />
                            </div>
                        </div>
                        <div v-else-if="!loading" class="noCommentCon">
                            <Empty :title="$t('empty.comment')" data="commentEmpty" />
                        </div>
                    </div>
                    <!-- 底部输入框 -->
                    <div class="bottom" v-if="detailData.state !== 1">
                        <div class="bottomInput">
                            <van-field v-model="input" ref="inputRef" :placeholder="placeholder"
                                @update:model-value="changeInput">
                            </van-field>
                            <div class="picture">
                                <van-uploader :max-count="1" :disabled="isPermossion" :after-read="afterRead"
                                    :beforeRead="beforeRead">
                                    <span v-if="showMask" @click="unloadImage" class="message"></span>
                                    <img src="@/assets/images/detail/picture.png" />
                                </van-uploader>
                            </div>
                            <div class="voice" @click="audio">
                                <img src="@/assets/images/detail/yuyin.png" />
                            </div>
                            <van-button class="confirm" v-show="showSendBtn" :disabled="isDisabled" @click="send"
                                type="primary">{{
                                    $t('detail.send') }}</van-button>
                        </div>
                        <div class="seleteImage" v-if="uploadImg.length">
                            <img :src="uploadImg" />
                            <img class="deleteImg" @click="deleteImg" src="@/assets/images/index/deleteComment.png" />
                        </div>
                    </div>

                </div>
                <div class="close" @click="close(index)">
                    <img src="@/assets/images/detail/close.png" />
                </div>

            </div>
            <div v-if="dialogFormPreview">
                <preview :data="detailData.attachmentUrl" :dialogFormPreview="dialogFormPreview"
                    :type="detailData.attachmentSuffix" @dialogClosePreview="dialogClosePreview"></preview>
            </div>
            <div v-if="dialogLikeList">
                <seeLikeList :data="detailData.likeList" :dialogLikeList="dialogLikeList" @dialogClose="dialogClose">
                </seeLikeList>
            </div>
            <AudioRecorder :isShowAudio="isShowAudio" @dialogCloseAudio="dialogCloseAudio" @audioSuccess="audioSuccess">
            </AudioRecorder>
        </van-popup>
    </div>

</template>
<script setup>
import { ref, watch, onMounted, nextTick, onUnmounted, onActivated, defineAsyncComponent } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant';
import { ymdDianTime } from "@/utils/time.js"
import CommentItem from "@/components/detail/commentItem.vue"
import translation from "@/components/common/translation.vue"
const Swiper = defineAsyncComponent(() =>
    import('@/components/index/swiper.vue')
);
const AudioRecorder = defineAsyncComponent(() =>
    import('@/components/common/audioRecorder.vue')
);
const preview = defineAsyncComponent(() =>
    import('@/components/common/preview.vue')
);
const seeLikeList = defineAsyncComponent(() =>
    import('@/components/detail/seeLikeList.vue')
);
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
const videoPlay = defineAsyncComponent(() =>
    import('@/components/common/video.vue')
);
const Empty = defineAsyncComponent(() =>
    import('@/components/common/empty.vue')
);
import { useRouter } from 'vue-router';
const router = useRouter()
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import emitter from '@/utils/mitt.js'
import { isSeleteDetailLike, isSeleteDetailCollect } from "@/assets/js/select.js"
import { getDynamicDetail, getComment, commentAdd, replyAdd, like, collect, uploadImage } from "@/api/home.js"
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import Compressor from 'compressorjs';
import { getDeviceType } from "@/utils/deviceType.js"
import _ from 'lodash';
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    },
    id: {
        type: String,
        default: ''
    },
    tooltipType: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: ''
    },
    index: {
        type: Number,
        require: false,
        default: null
    },
    isShowDetail: {
        type: Boolean,
        require: false,
        default: false
    }
})
const dialogLikeList = ref(false)
const dialogVisible = ref(false)
const input = ref('')
const inputRef = ref()
const commentData = ref([])
const detailData = ref({})
const showSendBtn = ref(false)
const isShowAudio = ref(false)
const uploadImg = ref('')
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const sendType = ref('comment')
const commentId = ref('')
const toUserId = ref('')
const replyType = ref('')
const translationCon = ref('')
const placeholder = ref('')
const imgCollectUrl = ref()
const imgLoveUrl = ref()
const loading = ref(true)
const fileList = ref([])
const dialogFormPreview = ref(false)
const isPermossion = ref(true)
const showMask = ref(true)
const isDisabled = ref(false)
onMounted(() => {
    // loading.value = true
    // getDetail(props.data.id)
    // getCommentList(props.data.id)
    const deviceType = getDeviceType();
    console.log(deviceType); // 输出 'Web', 'Mobile', 或 'iPad'
    // showToast('设备类型' + deviceType)
    if (deviceType == 'Web') {
        isPermossion.value = false
        showMask.value = false
    } else {
        // 判断是否有相机权限
        if (countStore.isCamera) {
            //    有权限
            isPermossion.value = false
            showMask.value = false
        }
    }
})
// 预览文件
const openFile = (() => {
    dialogFormPreview.value = true
})
// 关闭预览文件
const dialogClosePreview = (() => {
    dialogFormPreview.value = false
})
// 计算时间
const time = ((data) => {
    return ymdDianTime(data)
})
// 查看全部点赞list关闭
const dialogClose = () => {
    dialogLikeList.value = false
}
// 点击查看更多点赞list
const seeMoreLike = () => {
    dialogLikeList.value = true
}
// 点击上传图片
const unloadImage = () => {
    if (isPermossion.value) {
        // 先判断是否有权限 开启权限
        window.location.href = "uniwebview://requestCameraPerssion";
    }
}
//监听到返回是否开启权限
emitter.on('isUploadImage', (data) => {
    if (data == 'true') {
        isPermossion.value = false;
        countStore.isCamera = true
        showMask.value = false
        nextTick(() => {
            showToast(t('toast.uploadPrompt'))
        });
    }
})
// 文件读取前的钩子，返回 false 可以阻止文件读取
const beforeRead = (file) => {
    return true;
}
// 文件读取完成后的钩子
const afterRead = (file) => {
    console.log(file)
    showLoadingToast({
        forbidClick: true,
        duration: 0
    });
    // 示例使用 axios 进行文件上传
    if (file.file.size / 1024 / 1024 > 4) {
        return new Promise((resolve, reject) => {
            new Compressor(file.file, {
                // 压缩质量，0-1之间。数字越小，压缩比越高
                quality: 0.8,
                success(result) {
                    console.log(result)
                    // 默认返回result是blob类型的文件，可以转成file并返回，交给afterRead钩子进行上传
                    let newFile = new File([result], file.file.name, { type: file.file.type })
                    const sizeInMB = newFile.size / 1024 / 1024; // 转换为 MB 
                    console.log(sizeInMB); // 输出: 0.97
                    //判断大小
                    if (sizeInMB > 4) {
                        closeToast()
                        showToast(t('toast.imageShowToast'))
                        reject();

                    } else {
                        uploadFile(newFile);
                        resolve(newFile)
                    }
                },
                error(err) {
                    closeToast()
                    showToast(t('toast.imageShowToast'))
                    reject(err)
                },
            })
        })
    } else {
        uploadFile(file.file);
    }
}
const uploadFile = (file) => {
    const formData = new FormData();
    formData.append('file', file);
    uploadImage(formData)
        .then((res) => {
            // 接口调用成功之后的操作
            closeToast()
            console.log(res)
            if (res.code == 200) {
                showSendBtn.value = true
                uploadImg.value = res.url
            } else {
                showToast(t('toast.uploadImageFail'))
            }
        })
        .catch((err) => {
            // 接口调用失败之后的操作
            closeToast()
            console.log(err)
            showToast(t('toast.uploadImageFail'))
        })
}
const deleteImg = () => {
    if (isDisabled.value) {

    } else {
        uploadImg.value = ''
        if (input.value) {
            showSendBtn.value = true
        } else {
            showSendBtn.value = false
        }
    }


}
//获取到翻译组件传得值
const changeTranslationCon = (data) => {
    translationCon.value = data
}
// 点击语音
const audio = (() => {
    // isShowAudio.value = true
    // 给unity
    window.location.href = "uniwebview://requestpermission";
    countStore.audioType = 'detail'
})
emitter.on('openShowAudio', (audioType, data) => {
    if (audioType == 'detail') {
        isShowAudio.value = true
    }
})
// 
const dialogCloseAudio = ((data) => {
    console.log('关闭')
    isShowAudio.value = false
})
// 录音成功
const audioSuccess = ((data) => {
    console.log('转文字成功', data)
    input.value = input.value + data
    showSendBtn.value = true

})
// 输入框值改变
const changeInput = (value) => {
    console.log(value)
    console.log(input.value)
    if (input.value) {
        showSendBtn.value = true
    } else {
        if (uploadImg.value) {

        } else {
            showSendBtn.value = false
        }
    }
}
emitter.on('getReplyFocus', () => {
    console.log('sss')
    // focusInput()
})
const getFocus = (comment, toUser, userName, type) => {
    // 点击评论或回复获取焦点
    // commentId 评论的id
    // toUserId 被回复id 要回复消息发送者id
    // userId 回复id 登录用户id
    // const commentIdNum=String(comment)
    // const toUserIdNum=String(toUser)
    // console.log(commentIdNum,toUserIdNum)
    commentId.value = comment
    toUserId.value = toUser
    replyType.value = type
    sendType.value = 'reply'
    placeholder.value = t('detail.reply') + userName
    console.log('aaaa')
    focusInput()

}
const focusInput = () => {
    if (detailData.value.state == 1) {
        return;
    } else {
        nextTick(() => {
            inputRef.value.focus()
        })
    }

}
// 获取动态详情
const getDetail = (dyNamicsId) => {
    getDynamicDetail(dyNamicsId)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                detailData.value = res.data
                translationCon.value = res.data.content
                res.data.isLike ? imgLoveUrl.value = isSeleteDetailLike.selete : imgLoveUrl.value = isSeleteDetailLike.noselete
                res.data.isCollect ? imgCollectUrl.value = isSeleteDetailCollect.selete : imgCollectUrl.value = isSeleteDetailCollect.noselete
            } else if (res.code == 500) {
                const toast = showToast(t('toast.dynamicFail'));
                setTimeout(() => {
                    close()
                    if (res.data.videoFrame) {

                    } else {
                        // 刷新列表
                        emitter.emit('refurbishList')
                    }

                }, 2000); // 2秒后清除提示
            }
        })
        .catch(function (error) {
            console.log(error);
        });
};
// 获取评论列表
const getCommentList = (data) => {
    const query = {
        dynamicsId: data,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        limitId: commentData.value.length ? commentData.value[0].id : 0
    }
    getComment(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                total.value = res.total
                if (res.rows) {
                    if (pageNum.value == 1) {
                        commentData.value = res.rows
                        console.log('diyi')
                    } else {
                        res.rows.forEach((item) => {
                            commentData.value.push(item)
                        })
                        console.log('duo')
                    }
                } else {
                    commentData.value = []
                    console.log('kong')
                }
                loading.value = false
            }
        })
        .catch(function (error) {
            console.log(error);
        });
}
// 查看更多列表
const spreadMore = () => {
    pageNum.value++
    getCommentList(props.data.id)
}
// 监听到删除评论
emitter.on('deleteCommentList', (data) => {
    console.log(data)
    pageNum.value = 1
    commentData.value = []
    getCommentList(data)
    // commentData.value.splice(data, 1)
})
// 
// 点击点赞
const changeLike = (id) => {
    const query = {
        dyNamicsId: id,
        likeType: '0'
    }
    like(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                console.log(detailData.value.isLike)
                // 在详情点赞 通知对应列表更新状态
                emitter.emit('likeDetailSuccess', detailData.value.id)
                emitter.emit('likeListSuccess')
                if (detailData.value.isLike) {
                    detailData.value.likeNum--
                    detailData.value.isLike = 0
                    // showToast('已取消点赞');
                    console.log(t('toast.likeCancel'))
                    showToast(t('toast.likeCancel'));
                    imgLoveUrl.value = isSeleteDetailLike.noselete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('likeCancel', { like_count: 1 });
                } else {
                    detailData.value.likeNum++
                    detailData.value.isLike = 1
                    console.log(t('toast.likeSuccess'))
                    showToast(t('toast.likeSuccess'));
                    imgLoveUrl.value = isSeleteDetailLike.selete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('like', { like_count: 1 });
                }
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('toast.likeFail'))
        });
};
// 点击收藏
const changeCollect = (id) => {
    const query = {
        dyNamicsId: id,
    }
    collect(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                // 在详情收藏 通知对应列表更新状态
                emitter.emit('collectDetailSuccess', detailData.value.id)
                emitter.emit('collectSuccess')
                if (detailData.value.isCollect) {
                    detailData.value.collectNum--
                    detailData.value.isCollect = 0
                    // showToast('已取消点赞');
                    showToast(t('toast.collectCancel'));
                    imgCollectUrl.value = isSeleteDetailCollect.noselete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('collectCancel', { collect_count: 1 });

                } else {
                    detailData.value.collectNum++
                    detailData.value.isCollect = 1
                    showToast(t('toast.collectSuccess'));
                    imgCollectUrl.value = isSeleteDetailCollect.selete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('collect', { collect_count: 1 });
                }
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('collectFail.likeFail'))
        });
};
// 发送
const send = () => {
    // 判断是评论还是回复
    console.log(sendType.value)
    if (input.value.trim() || uploadImg.value) {
        if (sendType.value == 'comment') {
            // 评论
            sendComment()
            console.log('评论')
        } else {
            // 回复
            sendReply()
            console.log('回复')
        }
    }
}
// 添加评论
const sendComment = _.debounce(() => {
    isDisabled.value = true
    const query = {
        content: input.value,
        dyNamicsId: props.data.id,
        images: uploadImg.value ? uploadImg.value : ''
    }
    commentAdd(query)
        .then((res) => {
            console.log(res)
            isDisabled.value = false
            if (res.code === 200) {
                input.value = ''
                pageNum.value = 1
                commentData.value = []
                placeholder.value = ''
                uploadImg.value = ''
                getCommentList(props.data.id)
                showSendBtn.value = false
                // 使用数数的 SDK 记录点击事件
                ta.track('comment', { comment_count: 1 });
            } else if (res.code === 500) {
                if (res.msg == '4') {
                    const toast = showToast(t('toast.addCommentFail'));
                    setTimeout(() => {
                        close()
                    }, 2000); // 2秒后清除提示
                } else if (res.msg == '-5') {
                    showToast(t('toast.commentAfterDelete'));
                } else if (res.msg == '-6') {
                    showToast(t('toast.replyAfterDelete'));
                } else if (res.msg == '1') {
                    showToast({
                        message: t('toast.addDynamicViolation'),
                    });
                } else if (res.msg == '2') {
                    showToast({
                        message: t('toast.addTopicViolation'),
                    });
                } else if (res.msg == '3') {
                    showToast({
                        message: t('toast.addImageViolation'),
                    });
                } else {
                    showToast(t('toast.addCommentFail'));
                }

            } else {
                showToast(t('toast.addCommentFail'));
            }
        })
        .catch(function (error) {
            isDisabled.value = false
            console.log(error);
            showToast(t('toast.addCommentFail'));
        });
}, 1000, { leading: true, trailing: false })
// 添加回复
const sendReply = _.debounce(() => {
    isDisabled.value = true
    const query = {
        content: input.value,
        commentId: commentId.value,
        toUserId: toUserId.value,
        replyType: replyType.value,
        images: uploadImg.value ? uploadImg.value : ''
    }
    console.log(query)
    replyAdd(query)
        .then((res) => {
            console.log(res)
            isDisabled.value = false
            if (res.code === 200) {
                emitter.emit('addReplySuccess', commentId.value)
                input.value = ''
                pageNum.value = 1
                commentId.value = ''
                toUserId.value = ''
                replyType.value = ''
                sendType.value = 'comment'
                commentData.value = []
                placeholder.value = ''
                uploadImg.value = ''
                getCommentList(props.data.id)
                showSendBtn.value = false
                // 使用数数的 SDK 记录点击事件
                ta.track('comment', { comment_count: 1 });
            } else if (res.code === 500) {
                if (res.msg == '5') {
                    const toast = showToast(t('toast.addReplyFail'));
                    setTimeout(() => {
                        close()
                    }, 2000); // 2秒后清除提示
                } else if (res.msg == '-5') {
                    showToast(t('toast.commentAfterDelete'));
                } else if (res.msg == '-6') {
                    showToast(t('toast.replyAfterDelete'));
                } else if (res.msg == '1') {
                    showToast({
                        message: t('toast.addDynamicViolation'),
                    });
                } else if (res.msg == '2') {
                    showToast({
                        message: t('toast.addTopicViolation'),
                    });
                } else if (res.msg == '3') {
                    showToast({
                        message: t('toast.addImageViolation'),
                    });
                } else {
                    showToast(t('toast.addReplyFail'));
                }

            } else {
                console.log('添加回复失败');
                showToast(t('toast.addReplyFail'));
            }
        })
        .catch(function (error) {
            isDisabled.value = false
            console.log(error);
            showToast(t('toast.addReplyFail'));
        });
}, 1000, { leading: true, trailing: false })
// 定义组件的事件
const emits = defineEmits(['closePopup'])
// 像祖组件传递关闭事件
const close = (index) => {
    console.log(index)
    dialogVisible.value = false
    emits('closePopup')
}
// 点击弹出层触发
const onClickPopup = () => {
    console.log('点击事件')
}
//点击遮罩层触发
const onClickOverlay = () => {
}
// 关闭弹框
const closeDetail = () => {
    dialogVisible.value = false
    emits('closePopup')
}
watch(() => props.isShowDetail, (val) => {
    console.log(val, props.data.id)
    dialogVisible.value = val
    if (dialogVisible.value) {
        getDetail(props.data.id)
        commentData.value = []
        getCommentList(props.data.id)
    }
}, { immediate: true })

// 监听到关闭弹窗
emitter.on('closeDetail', (data) => {
    console.log('关闭关闭关闭')
    dialogVisible.value = false
    emits('closePopup')
})
// 监听到刷新评论列表
emitter.on('refurbishComment', (id) => {
    if (id == props.data.id) {
        console.log('刷新评论列表')
        commentData.value = []
        pageNum.value = 1
        getCommentList(props.data.id)
    }
})
// 监听到需要聚焦输入框
emitter.on('focusDetailInput', () => {
    console.log('收到聚焦输入框')
    focusInput()
})
onUnmounted(() => {
    emitter.off('getReplyFocus')
    emitter.off('deleteCommentList')
    emitter.off('openShowAudio')
    emitter.off('isUploadImage')
    emitter.off('closeDetail')
    emitter.off('focusDetailInput')
})
</script>

<style scoped lang="scss">
:deep(.van-overlay) {
    background: rgba(0, 0, 0, 0.4);
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2000;
}

:deep(.van-popup) {
    width: 85%;
    position: fixed !important;
    z-index: 2001;
}

.van-popup--center {
    width: 85%;
    max-width: 95vw;
}

.container {
    position: relative;
    display: flex;
    overflow: hidden;
    height: 85vh;
    max-height: 90vh;
    width: 100%;
    box-sizing: border-box;

    .leftCon {
        width: 720px;
        max-width: 40%;
        min-width: 300px;
        height: 100%;
        display: flex;
        flex-direction: column;
        overflow: hidden;

        .top {
            padding-top: 24px;
            display: flex;
            padding-left: 24px;
            padding-right: 14px;

            .material {
                flex: 1;
                display: flex;

                .avatar {
                    width: 80px;
                    height: 80px;
                }

                .info {
                    flex: 1;
                    margin-left: 10px;

                    .name {
                        font-size: var(--size_26);
                        color: var(--mainTtileColor);
                        padding-bottom: 2px;
                        font-family: var(--weight5);
                        min-height: 37px;
                    }

                    .time {
                        font-size: var(--size_24);
                        color: var(--bearTextColor);
                    }
                }

            }

            .rightBtn {
                display: flex;
                height: 32px;
                font-size: 26px;

                .translation {
                    width: 144px;
                    height: 32px;
                    text-align: center;
                    color: #BBE7FF;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: var(--size_20);
                    text-shadow: 0px 1px 1px rgba(25, 66, 96, 0.87);
                    background: url('@/assets/images/detail/fanyiBg.png') no-repeat;
                    background-size: 100% 100%;
                    // margin-right: 22px;
                }

                .more {
                    width: 28px;
                    height: 28px;
                    margin-left: 22px;
                    font-size: 26px;
                }
            }

        }

        .centerCon {
            flex: 1;
            overflow: auto;
            margin: 0 24px;

            .topic {
                margin: 16px 0 10px;
                color: #425E81;
                font-size: 25px;
                word-wrap: break-word;

                span {
                    padding-right: 10px;
                }
            }

            .msg {
                margin: 16px 0 30px 0;
                font-size: var(--size_22);
                color: var(--mainTtileColor);
                line-height: 32px;
                word-wrap: break-word;
                white-space: pre-wrap;
                /* 保持换行 */

            }

            .seeFile {
                margin-top: 16px;
                margin-bottom: 15px;
                background: rgba(81, 149, 231, 0.1);
                display: flex;
                align-items: center;
                padding: 0 16px;
                font-size: var(--size_22);
                height: 42px;

                img {
                    width: 30px;
                    height: 30px;
                }

                .name {
                    margin-left: 10px;
                    flex: 1;
                    color: var(--mainTtileColor);
                    white-space: nowrap;
                    /* 禁止文本换行 */
                    overflow: hidden;
                    /* 隐藏超出宽度的文本 */
                    text-overflow: ellipsis;
                    /* 在文本超出宽度时显示省略号 */
                }

                .size {
                    color: #5195E7;
                }

            }



        }

        .like {
            display: flex;
            align-items: center;
            justify-content: space-between;
            // height: 44px;
            padding: 10px 0;
            font-size: 24px;
            color: #666666;
            background: #F5F5F5;
            margin: 12px 24px 10px;

            .likeCon {
                flex: 1;
                display: -webkit-box;
                /* OLD - iOS 6-, Safari 3.1-6 */
                display: -moz-box;
                /* OLD - Firefox 19- (buggy) */
                display: -ms-flexbox;
                /* TWEENER - IE 10 */
                display: -webkit-flex;
                /* NEW - Chrome 21-28, Safari 6.1+ */
                display: flex;
                /* NEW - Modern Browsers */
                -webkit-box-align: center;
                /* OLD - iOS 6-, Safari 3.1-6 */
                -moz-box-align: center;
                /* OLD - Firefox 19- */
                -ms-flex-align: center;
                /* IE 10 */
                -webkit-align-items: center;
                /* Chrome 21-28, Safari 6.1+ */
                align-items: center;

                .likeList {
                    overflow: hidden; //超出的文本隐藏
                    display: -webkit-box;
                    -webkit-line-clamp: 1; // 超出多少行
                    -webkit-box-orient: vertical;

                    .showSpace {
                        white-space: pre;
                    }

                    .like-item {
                        display: inline-flex;
                        align-items: center;
                        direction: ltr;
                    }

                    .item-text {
                        unicode-bidi: isolate;
                    }

                    .separator {
                        display: inline-block;
                        margin: 0 2px;
                    }
                }
            }

            img {
                width: 32px;
                height: 32px;
                margin-left: 6px;
            }



        }

        .bottomBtn {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            height: 49px;
            border-top: 1px solid #E5E5E5;

            .bottomItem {
                display: flex;
                justify-content: center;
                align-items: center;
                font-size: var(--size_20);
                color: var(--bearTextColor);

                img {
                    width: 32px;
                    height: 32px;
                    margin-right: 28px;
                }
            }

            .rightBorder {
                border-right: 1px solid #E5E5E5;
            }
        }
    }

    .rightCon {
        height: 100%;
        overflow: hidden;
        flex: 1;
        display: flex;
        flex-direction: column;
        background: linear-gradient(90deg, rgba(241, 241, 241, 0.49) 0%, rgba(255, 255, 255, 0) 100%);

        .comment {
            flex: 1;
            overflow: hidden;
            padding: 22px 16px;
            display: flex;
            flex-direction: column;

            .title {
                font-weight: var(--weight5);
                color: var(--mainTtileColor);
                font-size: var(--size_20);
                margin-bottom: 20px;
            }

            .commentCon {
                flex: 1;
                overflow: auto;
            }

            .spreadMore {
                display: flex;
                align-items: center;
                justify-content: center;
                color: var(--bearTtileColor);
                font-size: var(--size_20);

                img {
                    margin-left: 2px;
                    width: 24px;
                    height: 24px;
                }
            }

            .noCommentCon {
                flex: 1;
                display: flex;
                justify-content: center;
                align-items: center;

            }
        }

        .bottom {
            .bottomInput {
                display: flex;
                align-items: center;
                padding: 14px 16px;
                height: 50px;


                :deep(.van-cell) {
                    flex: 1;
                    background-color: #F3F3F3;
                    border-color: #F3F3F3;
                    height: 50px;
                    box-shadow: none;
                }

                :deep(.van-cell__value) {
                    font-size: var(--size_24);
                }

                .confirm {
                    display: flex;
                    align-items: center;
                    margin-left: 12px;
                    //width: 84px;
                    height: 42px;
                    background: #5195E7;
                    border-radius: 4px;
                    color: #FFFFFF;
                    font-size: 22px;
                    text-align: center;
                    padding: 0 20px;
                }

                .picture {
                    margin-left: 12px;
                    position: relative;

                    .message {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 48px;
                        height: 48px;
                        z-index: 111;
                    }


                    img {
                        width: 48px;
                        height: 48px;
                    }
                }

                .voice {
                    margin-right: 8px;
                    margin-left: 12px;

                    img {
                        width: 48px;
                        height: 48px;
                    }

                }
            }

            .seleteImage {
                position: relative;
                width: 80px;
                padding-left: 16px;

                img {
                    width: 94px;
                    height: 94px;
                    object-fit: cover;
                    /* 图片等比缩放并覆盖容器 */
                    object-position: center;
                    /* 图片居中显示 */
                }

                .deleteImg {
                    position: absolute;
                    top: -20px;
                    right: -30px;
                    width: 38px;
                    height: 38px;
                }
            }
        }

    }

    .close {
        position: absolute;
        top: 10px;
        right: 18px;
        z-index: 999;

        img {
            width: 52px;
            height: 52px;
        }
    }
}
</style>
