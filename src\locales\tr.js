const tr = {
    index:{
        title:'<PERSON> say<PERSON> gönder<PERSON>',
        menuItems: {
            Dynamic: '<PERSON><PERSON><PERSON><PERSON>',
            Favorites: '<PERSON><PERSON>',
            Liked: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
            Messages: '<PERSON><PERSON>',
            weChat:'<PERSON><PERSON>',
            myDynamic:'<PERSON><PERSON><PERSON><PERSON><PERSON>',
            myFavorites:'<PERSON><PERSON><PERSON><PERSON><PERSON>',
            myLiked:'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>',
            Hot:'Trendler',
            Recommend:'<PERSON><PERSON><PERSON><PERSON>',
            New:'En yeni',
            Vote:'Tahmin&Kazanmak',
        },
        user:{
            praised:'<PERSON><PERSON><PERSON> kazan',
            collect:'<PERSON><PERSON>',
            chat:'Sohbet',
            addFriends:'Arkadaş ekle',
            friendRequest:'Arkadaşlık İsteği',
            verificationMessage:'Doğ<PERSON>lama Bilgisi',
            sendNow:'He<PERSON>',
            verificationMessagePlaceholder:'Nasılsın! Tanıştığımıza memnun oldum.',
        },
        msg:{
            likeCollectTab:'<PERSON><PERSON><PERSON> ve favori ekle',
            commentAitTab:"<PERSON><PERSON><PERSON> ve {'@'}",
            reply:'<PERSON>va<PERSON><PERSON>',
            likeCond:'<PERSON><PERSON><PERSON><PERSON> beğendim',
            likeConc:'Yo<PERSON>unu beğendi',
            likeConr:'<PERSON><PERSON>b<PERSON>n<PERSON> beğendi',
            collectCon:'Gönderini kaydettim',
            commentCon:'Gönderine yorum yaptım',
            replyCon:'Cevap verildi',
            aitCon:"{'@'}sen"
        }
    },
    detail:{
      replyNum:'Toplam %num% cevap var',
      seeMore:'Daha fazla gör',
      stop:'Kapat',
      originalText:'Orijinal metin',
      translation:'Çevir',
      commentNum:'yorum',
      send:'Gönder',
      reply:'Cevapla',
      replied:'Cevap verildi',
    //   have:'已有',
      likeListTitle:'%num% arkadaşın beğenisinden gelen beğeni'
    },
    add:{
       title:'Gönder',
       input:'Lütfen gönderi içeriğini girin',
       topic:'Konu ekle',
       whoSee:{
          title:'Kime gösterebilirim',
          all:'Herkes',
          friend:'Arkadaşlar görebilir',
          oneself:'Sadece kendine görünür',
       },
       publish:'Paylaş',
       audioStart:'Konuşmak için tıklayın',
       audioEnd:'Sonlandırmak için tıklayın',
       searchPlaceholder:'Arkadaşlarını ara',
       confirm:'Onayla',
       audioPermission:'Lütfen önce mikrofon iznini açın',
       imagePermission:'Lütfen önce kamera izinlerini etkinleştirin.',
       aitUser:"{'@'}kullanıcı"
    },
    report:{
       next:'Sonraki adım',
       confirmReport:'Raporu onayla',
       placeholder:'Lütfen içerik girin',
       detailTitle:'Ayrıntılı açıklama',
       imgTitle:'Görüntü kanıtı',
       reportSuccess:'Rapor başarılı',
       reportFail:'Rapor başarısız',
       reportSuccessInfo:'Gönderimden sonra, platform aktif olarak doğrulama ve işleme yapacaktır; sosyal ortamı koruma çabanız için teşekkür ederiz!',
       reportPublish:'Dinamikleri geri döndür',
       reportOneTitleAfterD:'%name% gönderisini bildiriyorsunuz',
       reportOneTitleAfterC:'%name% için yorumları bildiriyorsunuz',
       reportOneTitleAfterR:'%name% cevabını bildiriyorsunuz',
       reportTwoTitle:'%title% seçtiniz',
       reportEndTitleAfterD:"%name% gönderilen raporunuz %title%'e aittir",
       reportEndTitleAfterC:"%name% yorumlarınız %title%'e aittir",
       reportEndTitleAfterR:"%name% cevap raporunuz %title%'e aittir",
    },
    tooltip:{
        delete:'Sil',
        modify:'Değiştir',
        cancelCollect:'Favoriden çıkar',
        report:'Rapor et',
        block:'Engelle',
    },
    delete:{
      deleteCon:'Bu içeriği silmek istediğinize emin misiniz?',
      deleteCancel:'İptal et',
      deleteConfirm:'Onayla',
      blockCon:'Emin misiniz ki engellemek istiyor musunuz?',
    },
    toast:{
        likeSuccess:'Beğeni başarılı',
        likeCancel:'Beğeni iptal edildi.',
        likeFail:'Beğeni başarısız',
        collectSuccess:'Favori başarılı',
        collectCancel:'Favoriden çıkarıldı',
        collectFail:'Favori başarısız',
        publishSuccess:'Gönderi başarılı',
        publishFail:'Gönderi başarısız',
        modifySuccess:'Değişiklik başarılı',
        topicInfo:'En fazla 5 konu seçebilirsiniz',
        aitInfo:"En fazla {'@'}5 kullanıcı",
        ait:"Lütfen {'@'}'den önce en az 1 karakter girin",
        dynamicInput:'Lütfen aşağıdaki öğelerden en az birini yükleyin: dinamik içerik, görüntüler veya videolar',
        nextInfo:'Lütfen önce bir seçenek seçin',
        reportSuccess:'Rapor başarılı',
        reportFail:'Rapor başarısız',
        audioTextSuccess:'Sesli metne çeviri başarılı',
        audioTextFail:'Sesli metne çeviri başarısız',
        translationSuccess:'Çeviri başarılı',
        translationFail:'Çeviri başarısız',
        uploadImageFail:'Yükleme başarısız',
        deleteSuccess:'Silme başarılı',
        deleteFail:'Silme başarısız',
        // 新加
        imageLimit:"Dosya boyutu %num%MB'ı geçemez",
        imageNum:'9 görüntü yüklenebilir',
        uploadPrompt:'Lütfen resim ve video yüklemek için tıklayın',
        filePrompt:"(Doc, docx ve pdf dosya formatları desteklenir ve dosya boyutu 5mb'yi aşamaz)",
        imageBefore:"Tek bir görüntü 4 mb'den fazla olmayacak",
        imageShowToast:"Yükleme dosyası çok büyük",
        audioFail:'Kayıt sonlandırmada hata',
        collectCancelFail:'İptal başarısız',
        collectCancelSuccess:'İptal başarılı',
        dynamicFail:'Gönderi mevcut değil',
        addCommentViolation:'Gönderdiğiniz içerik düzenlemeleri ihlal ediyor olarak şüphelenilmektedir, lütfen düzenleyin ve yeniden gönderin. Düzenleyin ve yeniden gönderin.',
        addCommentFail:'Yorum ekleme başarısız',
        addReplyFail:'Cevap ekleme başarısız',
        addDynamicViolation:'Gönderdiğiniz "gönderi" içeriği düzenlemeleri ihlal ediyor olarak şüphelenilmektedir, lütfen düzenleyin ve yeniden gönderin.',
        addTopicViolation:'Gönderdiğiniz "konu" içeriği düzenlemeleri ihlal ediyor olarak şüphelenilmektedir, lütfen düzenleyin ve yeniden gönderin.',
        addImageViolation:'Gönderdiğiniz "görüntü" içeriği düzenlemeleri ihlal ediyor olarak şüphelenilmektedir, lütfen düzenleyin ve yeniden gönderin.',
        topicCon:'Konu içeriği boş olamaz.',
        getMsgFail:'Bilgi alınamadı',
        loginFail:'Giriş başarısız',
        aitInfoPermission:'Şu anda sadece kendine görünür',
        alreadyReport:'Birden fazla kez rapor ettiniz, lütfen platform geri bildirimini bekleyin',
        commentAfterDelete:'Yorum Silindi',
        replyAfterDelete:'Yanıtlama silindi',
        msgDataListFail:'Veri alma işlemi başarısız oldu',
        videoLimit:"Video 25 MB'ı geçmemelidir",
        videoPrompt:'En fazla 1 video yükleyebilirsiniz',
        videoToast:'Sadece resim veya video yüklenebilir',
        imageTitle:'Resim yükle',
        videoTitle:'Video yükle',
        applySuccess:'İsteği Başarıyla Gönderildi',
        applyFail:'İsteği Gönderme Başarısız',
        blacklistPrompt:"Karaliste'den Arkadaş Ekleme Yapılamaz",
        friendNumPrompt:'Diğer Kullanıcının Arkadaş Sayısı Dolu',
        myNumPrompt:'Mevcut Arkadaş Sayısı Dolu',
        failedPrompt:'Parametre Hatası',
        alignPrompt:'Bu Kişiyi Zaten Arkadaştan Ekledebildiniz, İsteği Tekrar Gönderemezsiniz',
        applyMyPrompt:'Kendini Ekleyemezsin',
        alignApply:'Zaten arkadaşlık isteği gönderildi. 48 saat sonra tekrar gönderilebilir',
        blockSuccess:'Kullanıcı kara listeye eklendi',
        blockFail:'Engelleme başarısız oldu',
        blockListFull:'Engelleme listesi dolu',
        checkAgreementPrompt:"《İçerik Yayın Bildirisi》i kabul etmediniz, gelişmeleri yayınlayamazsınız",
        AgreementFile:'Belgeyi okudunuz ve onayladınız',
        fileTitle:'《İçerik Yayın Bildirisi》',
        sameLanguagePrompt:'Şu anda aynı dil kullanılıyor, çeviri gerekli değil',

    },
    vote:{
        voteProgress:'Devam ediyor',
        voteEnd:'Sonlandı',
        voteSettle:'Ödenmiş',
        oneselfNum:'Oyladı',
        voteNum:'{num} Para',
        timeName:'Kalan süre',
        allNum:'Toplam para miktarı',
        participateInVoting:'Toplam Oyuncu Sayısı:',
        getCoins:'Bu sefer, {num} coin kazandınız',
        voteBtn:'Seçmek',
        voteTitle:'{num} için oylamak',
        inputInfo:'Lütfen miktarı seçin',
        voteConfirm:'Onayla',
        voteSuccess:'Başarı',
        voteFail:'Başarısızlık',
        statusEnd:'Etkinlik sona erdi',
        voteTnfo:"Etkinliğe katılmak için gerekli olan en düşük madeni para miktarı 1'dir",
        hold:'Sahip olmak',
        balance:'Mevcut hesap bakiyeniz yetersiz. Lütfen zamanında şarj yapın',
        remainingTimeData:'{days} gün {hours} saat {minutes} dakika {seconds} saniye',
        questionInfo:'Kullanıcının tüm jetonları bu etkinliğin ödül havuzuna gidecek ve doğru tahmin eden kullanıcılar, tahmin ettikleri jetonların sayısına göre ödül havuzundaki tüm 【zaman-uzay jetonları】ı bölecekler.',
    },
    video:{
        videoIndex:'Bir şey söyle...',
        videoDetail:'Detay sayfası',
        videoTitle:'Video',
    },
    empty:{
        comment:'Henüz yorum yok',
        list:'Veri mevcut değil',
        content:'İçerik mevcut değil',
        message:'Mesaj yok'
    }
}

export default tr;

