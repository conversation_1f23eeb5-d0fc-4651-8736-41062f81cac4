<template>
    <div class="swipe-container">
        <van-swipe ref="swipe" :autoplay="2000" class="my-swipe" indicator-color="white">
            <van-swipe-item v-for="(item, index) in data" :key="item">
                <img :src="item" @click.stop="showImage(index)" v-lazy="item" />
            </van-swipe-item>
        </van-swipe>
        <div class="arrow-left" :class="[isShowArrow ? '' : 'hiddenArrow']" @click="prev">
            <van-icon name="arrow-left" />
        </div>
        <div class="arrow-right" :class="[isShowArrow ? '' : 'hiddenArrow']" @click="next">
            <van-icon name="arrow" />
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted } from 'vue';
import { showImagePreview } from 'vant';
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    }
});
const swipe = ref(null);
const isShowArrow = ref(true)
const prev = () => {
    swipe.value.prev();
};

const next = () => {
    swipe.value.next();
};
const showImage = (index) => {
    console.log(index)
    showImagePreview({
        images: props.data,
        startPosition: index,
    });
};
onMounted(() => {
    if (props.data.length > 1) {

    } else {
        isShowArrow.value = false
    }
})
</script>

<style scoped lang="scss">
.swipe-container {
    position: relative;
    width: 100%;
    height: 348px;
    margin-bottom: 20px;

    .van-swipe {
        width: 100%;
        height: 100%;
    }

    .van-swipe-item {
        img {
            height: 100%;
            width: 100%;
            object-fit: contain;
        }
    }
}

.swipe-container .arrow-left,
.swipe-container .arrow-right {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    color: #fff;
    background-color: rgba(185, 180, 180, 0.5);
    border-radius: 50%;
    width: 25px;
    height: 25px;
    // padding: 5px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
}

.swipe-container .arrow-left {
    left: 10px;
}

.swipe-container .arrow-right {
    right: 10px;
}

:deep(.van-icon) {
    // width: 25px;
    // height: 25px;
    font-size: 20px;
    // font: 100;
}

.hiddenArrow {
    opacity: 0;
}
</style>
