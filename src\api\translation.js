import request from "@/axios/axios2";
import axios from 'axios';
let cancelToken = axios.CancelToken;
let cancel;
let cancelTokens = [];
// http://192.168.1.138:5001/two/translate
// http://18.221.64.66/translate/submit
// 翻译
// 导出一个名为translation的函数，用于翻译数据
export function translation(data) {
    const CancelToken = axios.CancelToken;
    const source = CancelToken.source();
    cancelTokens.push(source.cancel);
    return request({
        url: '/translate/submit',
        method: 'POST',
        urlType: 'api1',
        data,
        cancelToken: source.token,
      
    })
}
export function axiosCancel() {
    while (cancelTokens.length > 0) {
        cancelTokens.pop()();
    }
  }
// 语音转文字
export function rcvaudio(data) {
    return request({
        url: '/rcvaudio',
        method: 'POST',
        urlType: 'api2',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        data
    })
}
// 登录
export function login(data) {
    return request({
        url: '/login',
        method: 'POST',
        urlType: 'api1',
        data
    })
}
export function applyFriend(data) {
    return request({
        url: '/gs/web/applyFriend',
        method: 'POST',
        urlType: 'api3',
        data
    })
}