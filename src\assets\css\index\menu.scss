.menu {
    display: flex;

    .memuCon {
        font-size: 28px;
        position: relative;
        background: url("@/assets/images/index/leftMenuBg.webp") no-repeat;
        background-size: 100% 100%;

        .menuItem {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 27px 31px 0 30px;
            color: #FFFFFF;
            font-size: 30px;

            img {
                width: 40px;
                height: 40px;
            }
        }

        .otherMenu {
            padding-right: 120px;
        }

        .van-sidebar {
            width: 100%;

            .van-sidebar-item {
                height: 80px;
                font-size: 28px;
                padding: 0 36px 0 20px;
                color: #FFFFFF;
                margin-left: 50px;
                margin-right: -2px;
                background: none;
                font-weight: 400;

                .menuItemCon {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    position: relative;

                    .badge {
                        position: absolute;
                        top: 10px;
                        right: -32px;
                        width: 32px;
                        height: 32px;
                        line-height: 32px;
                        text-align: center;
                        background: #F64040;
                        border-radius: 50%;
                        font-size: 18px;
                        color: #FFFFFF;

                    }
                }
            }

            .van-sidebar-item--select:before {
                display: none;
            }

            .van-sidebar-item--select,
            .van-sidebar-item--select:active {
                color: #318DE9;
                background: url("@/assets/images/index/leftMenuActive.webp") no-repeat;
                background-size: 100% 100%;
                text-shadow: 0px 1px 1px rgba(0, 0, 0, 0.5);
            }

            :deep(.van-badge__wrapper) {
                display: block;
            }

            :deep(.van-sidebar-item__text) {
                text-align: right;
                display: flex;
                align-items: center;
                justify-content: flex-end;
                height: 80px;
            }

        }
    }

    .menuSh {
        width: 18px;
        background: url("@/assets/images/index/leftMenuBgShow.webp") no-repeat;
    }

}