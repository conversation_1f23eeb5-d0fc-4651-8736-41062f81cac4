import request from "@/axios/axios";
// 获取赛事title
export function getMatchTitle() {
    return request({
        url: `/system/category/front/list`,
        method: 'GET',
    })
}
// 获取赛事列表
export function getMatchList(params) {
    return request({
        url: `/system/contest/front/list`,
        method: 'GET',
        params
    })
}
// 获取赛事详情
export function getMatchDetail(id) {
    return request({
        url: `/system/contest/front/${id}`,
        method: 'GET',
    })
}
// 获取赛事详情参赛作品
export function getMatchWorks(params) {
    return request({
        url: `/system/contest/detail/front/list`,
        method: 'GET',
        params
    })
}
// 获取详情我的参赛作品
export function getMyMatchWorks(params) {
    return request({
        url: `/system/contest/detail/front/person?`,
        method: 'GET',
        params
    })
}
// 发布参赛作品
export function publishWorks(data) {
    return request({
        url: `/system/contest/detail`,
        method: 'POST',
        data
    })
}
//举报参赛作品
export function reportWorks(data) {
    return request({
        url: `/system/contest/complain/front`,
        method: 'POST',
        data
    })
}
// 判断是否有资格上传作品
export function hasUploadWorks(params) {
    return request({
        url: `/system/contest/detail/preInspect`,
        method: 'GET',
        params
    })
}
//我参加的赛事
export function myMatchList(params) {
    return request({
        url: '/system/contest/front/my/list',
        method: 'GET',
        params
    })
}
// 投票
export function voteWorks(data) {
    return request({
        url: '/system/contest/vote',
        method: 'POST',
        data
    })
}
// 获取赛事视频列表
export function getMatchVideoList(params) {
    return request({
        url: '/system/contest/detail/front/video/list',
        method: 'GET',
        params
    })
}