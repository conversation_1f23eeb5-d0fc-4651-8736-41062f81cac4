const fr = {
    index:{
        title:"Page d'accueil",
        menuItems: {
            Dynamic: 'Publications',
            Favorites: 'Collecte',
            Liked: 'Aimé',
            Messages: 'Message',
            weChat:'Moments',
            myDynamic:'Mes publications',
            myFavorites:'Mes favoris',
            myLiked:'Mes aimés',
            Hot:'Sujets populaires',
            Recommend:'Recommander',
            New:'Dernier',
            Vote:'<PERSON><PERSON>&Gagner',
        },
        user:{
            praised:'Gagner des likes',
            collect:'Collecte',
            chat:'Chat',
            addFriends:'Ajouter des amis',
            friendRequest:'Informations de validation',
            verificationMessage:"Vérification d'information",
            sendNow:'Envoyer immédiatement',
            verificationMessagePlaceholder:'Comment ça va ! Enchanté de vous rencontrer',
        },
        msg:{
            likeCollectTab:'Gagner des likes',
            commentAitTab:"Commentaires et{'@'}",
            reply:'Répondre',
            likeCond:"J'ai aimé votre publication",
            likeConc:"J'ai aimé votre commentaire",
            likeConr:"J'ai aimé votre réponse",
            collectCon:"J'ai enregistré votre publication",
            commentCon:'Commenté sur votre publication',
            replyCon:'Répondu',
            aitCon:"{'@'}vous"
        }
    },
    detail:{
      replyNum:'Un total de %num% réponses',
      seeMore:'Voir plus',
      stop:'Plier',
      originalText:'Texte original',
      translation:'Traduire',
      commentNum:'commentaires',
      send:'Envoyer',
      reply:'Répondre',
      replied:'Répondu',
    //   have:'已有',
      likeListTitle:"J'aime de %num% amis"
    },
    add:{
       title:'Publier',
       input:'Veuillez entrer le contenu de la publication',
       topic:'Ajouter un sujet',
       whoSee:{
          title:'À qui puis-je le montrer',
          all:'Tout le monde',
          friend:'Les amis peuvent voir',
          oneself:'Visible uniquement pour vous',
       },
       publish:'Libérer',
       audioStart:'Cliquez pour parler',
       audioEnd:'Cliquez pour terminer',
       searchPlaceholder:'Recherchez vos amis',
       confirm:'Confirmer',
       audioPermission:"Veuillez d'abord ouvrir la permission du microphone",
       imagePermission:"Veuillez d'abord activer les permissions de la caméra",
       aitUser:"{'@'}utilisateur"
    },
    report:{
       next:'Prochaine étape',
       confirmReport:'Confirmer le rapport',
       placeholder:'Veuillez entrer du contenu',
       detailTitle:'Description détaillée',
       imgTitle:"Preuve d'image",
       reportSuccess:'Signalement réussi',
       reportFail:'Échec du signalement',
       reportSuccessInfo:"Après soumission, la plateforme vérifiera et traitera activement ; merci pour votre effort à maintenir l'environnement social !",
       reportPublish:'Retourner les dynamiques',
       reportOneTitleAfterD:'Vous signalez le post de %name%',
       reportOneTitleAfterC:'Vous signalez un commentaire de %name%',
       reportOneTitleAfterR:'Vous signalez la réponse de %name%',
       reportTwoTitle:'Vous avez sélectionné %title%',
       reportEndTitleAfterD:'Votre rapport pour le post de %name% appartient à %title%',
       reportEndTitleAfterC:'Votre rapport pour commentaires de %name% appartient à %title%',
       reportEndTitleAfterR:'Votre rapport pour les réponses de %name% appartient à %title%',
    },
    tooltip:{
        delete:'Supprimer',
        modify:'Modifier',
        cancelCollect:'Ne plus aimer',
        report:'Signaler',
        block:'Bloquer',
    },
    delete:{
      deleteCon:'Êtes-vous sûr de vouloir supprimer ce contenu ?',
      deleteCancel:'Annuler',
      deleteConfirm:'Confirmer',
      blockCon:'Voulez-vous vraiment bloquer ?',
    },
    toast:{
        likeSuccess:'Aime réussi',
        likeCancel:`Le "j'aime" a été annulé`,
        likeFail:`Échec du "j'aime"`,
        collectSuccess:'Favori réussi',
        collectCancel:'Non favorisé',
        collectFail:`Échec de l'ajout aux favoris`,
        publishSuccess:'Publication réussie',
        publishFail:'Échec de la publication',
        modifySuccess:'Correction réussie',
        topicInfo:"Vous pouvez choisir jusqu'à 5 sujets",
        aitInfo:"Jusqu'à 5 utilisateurs",
        ait:"Veuillez entrer au moins 1 caractère avant {'@'}",
        dynamicInput:"Veuillez télécharger au moins l'un des éléments suivants : contenu dynamique, images ou vidéos",
        nextInfo:"Veuillez d'abord sélectionner une option",
        reportSuccess:'Signalement réussi',
        reportFail:'Échec du signalement',
        audioTextSuccess:'Reconnaissance vocale réussie',
        audioTextFail:'Échec de la reconnaissance vocale',
        translationSuccess:'Traduction réussie',
        translationFail:'Échec de la traduction',
        uploadImageFail:'Échec du téléchargement',
        deleteSuccess:'Échec de la suppression',
        deleteFail:'Suppression réussie',
        // 新加
        imageLimit:'La taille du fichier ne peut pas dépasser %num% MB',
        imageNum:"Jusqu'à 9 images peuvent être téléchargées",
        uploadPrompt:"Veuillez cliquer pour télécharger des images et des vidéos",
        filePrompt:'(les formats de fichiers Doc, docx et pdf sont pris en charge, et la taille du fichier ne peut pas dépasser 5 mo)',
        imageBefore:'Pas plus de 4 Mo pour une seule image',
        imageShowToast:"Le fichier téléchargé est trop grand",
        audioFail:"Erreur de fin d'enregistrement",
        collectCancelFail:'Annulation échouée',
        collectCancelSuccess:'Annulation réussie',
        dynamicFail:"Le post n'existe pas",
        addCommentViolation:'Le contenu que vous avez soumis est suspecté de violer les règlements, veuillez le modifier et le soumettre à nouveau. Modifiez et soumettez à nouveau.',
        addCommentFail:"Échec de l'ajout du commentaire",
        addReplyFail:"Échec de l'ajout de la réponse",
        addDynamicViolation:'Le contenu du "post" que vous avez soumis est suspecté de violer les règlements, veuillez le modifier et le soumettre à nouveau.',
        addTopicViolation:'Le contenu du "sujet" que vous avez soumis est suspecté de violer les règlements, veuillez le modifier et le soumettre à nouveau.',
        addImageViolation:'Le contenu "image" que vous avez soumis est suspecté de violer les règlements, veuillez le modifier et le soumettre à nouveau.',
        topicCon:'Le contenu du sujet ne peut pas être vide',
        getMsgFail:'Échec de la récupération des informations',
        loginFail:'Échec de la connexion',
        aitInfoPermission:'Actuellement visible uniquement par vous-même',
        alreadyReport:'Vous avez signalé plusieurs fois, veuillez attendre le retour de la plateforme',
        commentAfterDelete:'Commentaire supprimé',
        replyAfterDelete:'Réponse supprimée',
        msgDataListFail:"Echec de l'acquisition des données",
        videoLimit:'La vidéo ne doit pas dépasser 25 Mo',
        videoPrompt:'Maximum 1 vidéo peut être téléchargée',
        videoToast:'Seules les images ou les vidéos peuvent être téléchargées',
        imageTitle:'Télécharger une image',
        videoTitle:'Télécharger une vidéo',
        applySuccess:'Demande envoyée avec succès',
        applyFail:"Échec de l'envoi de la demande",
        blacklistPrompt:"Impossible d'ajouter un ami à partir de la liste noire",
        friendNumPrompt:"Le nombre maximal d'amis de l'autre partie est atteint",
        myNumPrompt:"Le nombre maximal d'amis actuel est atteint",
        failedPrompt:'Erreur de paramètre',
        alignPrompt:'Vous avez déjà ajouté cette personne comme ami, vous ne pouvez pas soumettre une demande à nouveau',
        applyMyPrompt:"Impossible d'ajouter soi-même",
        alignApply:"Vous avez déjà envoyé une demande d'amitié. Vous pouvez en envoyer une autre après 48 heures",
        blockSuccess:"L'utilisateur a été ajouté à la liste noire",
        blockFail:'Échec du blocage',
        blockListFull:'La liste de blocage est pleine',
        checkAgreementPrompt:"Vous n'êtes pas d'accord avec 《Déclaration de publication de contenu》 et vous ne pouvez pas publier la dynamique",
        AgreementFile:'Vous avez lu et accepté le document',
        fileTitle:'《Déclaration de publication de contenu》',
        sameLanguagePrompt:'Actuellement dans la même langue, pas besoin de traduction',

    },
    vote:{
        voteProgress:'En cours',
        voteEnd:'Terminé',
        voteSettle:'Réglementé',
        oneselfNum:'A voté',
        voteNum:'{num} Pièce',
        timeName:"Temps restant",
        allNum:'Nombre total de pièces',
        participateInVoting:'Nombre total de joueurs:',
        getCoins:'Cette fois, vous avez gagné {num} pièces',
        voteBtn:'Choisir',
        voteTitle:'Voter pour {num}',
        inputInfo:'Veuillez sélectionner la quantité',
        voteConfirm:'Confirmer',
        voteSuccess:'Succès',
        voteFail:'Échec',
        statusEnd:"L'événement est terminé",
        voteTnfo:"Le nombre minimum de pièces nécessaires pour participer à l'événement est de 1",
        hold:'Détenir',
        balance:'Votre solde de compte actuel est insuffisant. Veuillez recharger rapidement',
        remainingTimeData:'{days} jours {hours} heures {minutes} min {seconds} sec',
        questionInfo:"Toutes les pièces des utilisateurs seront versées dans le pool de prix de cet événement, et les utilisateurs qui devinent correctement diviseront toutes les 【pièces de temps-espace】 dans le pool de prix en fonction du nombre de pièces qu'ils ont deviné.",
    },
    video:{
        videoIndex:'Dis quelque chose...',
        videoDetail:'Page de détails',
        videoTitle:'Vidéo',
    },
    empty:{
        comment:'Aucun commentaire pour le moment',
        list:'Aucune donnée disponible',
        content:'Aucun contenu disponible',
        message:"Le message n'est pas disponible"
    }
}

export default fr;

