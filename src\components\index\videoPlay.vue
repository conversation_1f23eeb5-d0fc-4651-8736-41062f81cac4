<template>
    <div class="swipeItemCon">
        <div class="videoCon">
            <!-- 顶部控件 v-show="showControls"-->
            <div class="top-controls">
                <div class="back-button" @click="backTo">
                    <img src="@/assets/images/video/back.webp" />
                </div>
                <div class="material">
                    <avatar :url="isMatch ? data.avatar : data.avatart" :userId="isMatch ? data.userIdStr : data.userId" type="comment"></avatar>
                    <div class="name">{{ isMatch ? data.nickName : data.userName }}</div>
                </div>
                <div class="more-button">
                    <workPopover v-if="isMatch" :data="data" :complainType="data.voteType"></workPopover>
                    <popover v-else data="data" tooltipType="myFriend" imgType="videoPlay"></popover>

                </div>
            </div>

            <!-- 视频容器 -->
            <div class="video" @click="handleClick">
                <video ref="videoRef" preload="auto" loop x5-video-player-type="h5-page"
                    :x5-video-player-fullscreen="false" :webkit-playsinline="true" :x5-playsinline="true"
                    :playsinline="true" :fullscreen="false" class="fullscreenVideo" :poster="data.videoFrame"
                    @canplay="getVideoInfo()" @timeupdate="getCurrentInfo()" @play="onPlay" @pause="onPause">
                    <source :src="isMatch ? data.voteVideo : data.video" type="video/mp4" />
                    <!-- <source :src="video" type="video/mp4" /> -->
                </video>
            </div>
            <!-- 播放暂停图标 -->
            <div class="play-icon" v-show="showControls" @click.stop="changePlayIcon">
                <img :src="isPlaying ? pauseIcon : playIcon" class="control-icon" alt="播放控制">
            </div>

            <!-- 底部控制栏 -->
            <!-- v-show="showControls" -->
            <div class="bottom-controls">
                <!-- 进度条 -->
                <div class="time">{{ currentTime }} / {{ duration }}</div>
                <div class="progress-bar">
                    <div class="progress-bg" ref="control" @click="setProgress">
                        <div class="progress" :style="{ width: progressWidth + 'px' }"></div>

                    </div>
                </div>
                <!-- 互动按钮 -->
                <div v-if="isMatch" class="match-buttons">
                    <div class="match-left">
                        <img class="match-img" v-if="data.voteTop == 1" src="@/assets/images/match/first.webp" alt="">
                        <img class="match-img" v-if="data.voteTop == 2" src="@/assets/images/match/second.webp" alt="">
                        <img class="match-img" v-if="data.voteTop == 3" src="@/assets/images/match/thirdS.webp" alt="">
                        <div class="content">{{ data.content }}</div>
                    </div>
                    <!-- <div class="match-right">
                        <div class="num">200票</div>
                        <div class="vote">投票</div>
                    </div> -->
                    <Vote :data="data" :type="matchType" className="videoVote" />
                </div>

                <div v-else class="interaction-buttons">
                    <div class="button">
                        <div class="button-item">
                            <img :src="imgLoveUrl" @click.stop="changeLike(data.id)" />
                            <div>{{ data.likeNum }}</div>
                        </div>
                        <div class="button-item" @click="openComment(true)">
                            <img src="@/assets/images/video/comment.webp" />
                            <div>{{ data.commentNum }}</div>
                        </div>
                        <div class="button-item">
                            <img :src="imgCollectUrl" @click.stop="changeCollect(data.id)" />
                            <div>{{ data.collectNum }}</div>
                        </div>
                    </div>
                    <div class="comment-input">
                        <div class="inputBg" @click="openComment(true)">
                            <input type="text" readonly :placeholder="$t('video.videoIndex')" />
                        </div>

                    </div>
                    <!-- 详情按钮 -->
                    <div class="detailBtn" @click="openComment(false)">{{ $t('video.videoDetail') }}</div>
                    <!-- <van-button class="delete" @click="deleteItem">删除</van-button> -->
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, defineAsyncComponent, onBeforeUnmount } from "vue";
import { showToast } from 'vant';
import 'vant/lib/index.css';
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
import { isSeleteVideoLike, isSeleteVideoCollect } from "@/assets/js/select.js"
import { getDynamicDetail, like, collect } from "@/api/home.js"
const Vote = defineAsyncComponent(() =>
    import('@/components/match/vote.vue')
)
const workPopover = defineAsyncComponent(() =>
    import('@/components/match/workPopover.vue')
)
// 导入播放器图标
const playIcon = new URL('@/assets/images/video/play.png', import.meta.url).href
const pauseIcon = new URL('@/assets/images/video/pause.png', import.meta.url).href
import video from '@/assets/video/down.mp4'
const props = defineProps({
    data: {
        type: Object,
        required: false,
        default: () => ({
            title: '',
            src: '',
            avatar: '',
            username: '',
            likes: '0',
            comments: '0',
            shares: '0'
        })
    },
    autoPlay: {
        type: Boolean,
        default: true
    },
    isMatch: {
        type: Boolean,
        default: false
    },
    matchType: {
        type: String,
        default: ''
    }
});

const emit = defineEmits(['commentStateChange', 'videoStateChange', 'delete', 'back']);
const imgCollectUrl = ref()
const imgLoveUrl = ref()
// 控制播放状态
const isPlaying = ref(false);
const videoRef = ref(null);
const duration = ref('00:00');
const currentTime = ref('00:00');
const showControls = ref(false);
let controlsTimer = null;

// 点击相关变量
let lastClickTime = 0;
const DOUBLE_CLICK_THRESHOLD = 300;
// 点击返回按钮返回首页
const backTo = () => {
    // router.go(-1)
    // emitter.emit('homeBack')
    emit('back')
}
// 统一的点击处理函数
const handleClick = (event) => {
    event.stopPropagation();
    const currentTime = new Date().getTime();
    const timeDiff = currentTime - lastClickTime;
    togglePlay();
    toggleControls();
    console.log('单击')
    // if (timeDiff < DOUBLE_CLICK_THRESHOLD) {
    //     togglePlay();
    //     lastClickTime = 0;
    //     console.log('双击')
    // } else {
    //     // setTimeout(() => {
    //     //     if (currentTime === lastClickTime) {
    //     //         toggleControls();
    //     //     }
    //     // }, DOUBLE_CLICK_THRESHOLD);
    //     // lastClickTime = currentTime;
    //     // togglePlay();
    //     // toggleControls();
    //     // console.log('单击')
    // }
};

// 播放/暂停切换
const togglePlay = async () => {
    try {
        const video = videoRef.value;
        if (!video) {
            console.log('Video element not found');
            return;
        }

        if (video.paused) {
            await video.play().catch(error => {
                if (error.name !== 'AbortError') {
                    console.error('视频播放失败:', error);
                }
            });
            isPlaying.value = false;
            emit('videoStateChange', true);
        } else {
            video.pause();
            isPlaying.value = true;
            emit('videoStateChange', false);
        }
    } catch (error) {
        if (error.name !== 'AbortError') {
            console.error('视频控制失败:', error);
        }
    }
};
const changePlayIcon = () => {
    togglePlay();
    if (showControls.value) {
        controlsTimer = setTimeout(() => {
            showControls.value = false;
        }, 3000);
    }
}
// 控制界面显示/隐藏
const toggleControls = () => {
    showControls.value = !showControls.value;

    if (controlsTimer) {
        clearTimeout(controlsTimer);
    }

    if (showControls.value) {
        controlsTimer = setTimeout(() => {
            showControls.value = false;
        }, 3000);
    }
};

// 打开评论面板
const openComment = (focusInput = false) => {
    // 如果暂停不做操作 播放中的话暂停并显示暂停按钮
    const video = videoRef.value;
    if (!video) {
        console.log('Video element not found');
        return;
    }
    if (video.play) {
        video.pause();
        isPlaying.value = true;
        emit('videoStateChange', false);
    }
    showControls.value = true;

    // 标记评论已打开
    isCommentOpen.value = true;

    // 确保使用原始视口高度
    setViewportHeight();

    // 阻止滑动
    if (window.swiper) {
        window.swiper.allowTouchMove = false;
    }

    // 发送事件
    emit('commentStateChange', props.data, 'myFriend', true, focusInput);
};
// 点击点赞
const changeLike = (id) => {
    const query = {
        dyNamicsId: id,
        likeType: '0'
    }
    like(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                // // 在详情点赞 通知对应列表更新状态
                emitter.emit('likeVideoDetailSuccess', id)
                // emitter.emit('likeListSuccess')
                if (props.data.isLike) {
                    props.data.likeNum--
                    props.data.isLike = 0
                    // showToast('已取消点赞');
                    console.log(t('toast.likeCancel'))
                    showToast(t('toast.likeCancel'));
                    imgLoveUrl.value = isSeleteVideoLike.noselete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('likeCancel', { like_count: 1 });
                } else {
                    props.data.likeNum++
                    props.data.isLike = 1
                    console.log(t('toast.likeSuccess'))
                    showToast(t('toast.likeSuccess'));
                    imgLoveUrl.value = isSeleteVideoLike.selete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('like', { like_count: 1 });
                }
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('toast.likeFail'))
        });
};
// 点击收藏
const changeCollect = (id) => {
    const query = {
        dyNamicsId: id,
    }
    collect(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                // 在详情收藏 通知对应列表更新状态
                emitter.emit('collectVideoDetailSuccess', id)
                // emitter.emit('collectSuccess')
                if (props.data.isCollect) {
                    props.data.collectNum--
                    props.data.isCollect = 0
                    // showToast('已取消点赞');
                    showToast(t('toast.collectCancel'));
                    imgCollectUrl.value = isSeleteVideoCollect.noselete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('collectCancel', { collect_count: 1 });

                } else {
                    props.data.collectNum++
                    props.data.isCollect = 1
                    showToast(t('toast.collectSuccess'));
                    imgCollectUrl.value = isSeleteVideoCollect.selete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('collect', { collect_count: 1 });
                }
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('collectFail.likeFail'))
        });
};
// 获取视频信息
const getVideoInfo = () => {
    if (!videoRef.value) return;
    duration.value = formatTime(parseInt(videoRef.value.duration));
};

// 获取当前播放进度
const getCurrentInfo = () => {
    if (!videoRef.value) return;
    currentTime.value = formatTime(parseInt(videoRef.value.currentTime));

    const ratio = videoRef.value.currentTime / videoRef.value.duration;
    const control = document.querySelector('.progress-bg');
    if (control) {
        const allWidth = control.getBoundingClientRect().width;
        progressWidth.value = allWidth * ratio;
    }
};

// 设置播放进度
const progressWidth = ref(0);
const setProgress = (e) => {
    const control = e.currentTarget;
    const rect = control.getBoundingClientRect();
    const ratio = (e.clientX - rect.left) / rect.width;
    if (videoRef.value) {
        videoRef.value.currentTime = ratio * videoRef.value.duration;
    }
};

// 格式化时间
const formatTime = (seconds) => {
    if (seconds <= 0) return '00:00';
    const minutes = Math.floor(seconds / 60);
    seconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
};

// 视频可见性变化处理
const handleVisibilityChange = async (entries) => {
    const [entry] = entries;

    // 如果评论框打开，不处理可见性变化
    if (isCommentOpen.value) {
        return;
    }

    if (entry.isIntersecting) {
        // 广播"我可见了"，带上当前视频 id
        emitter.emit('videoVisible', props.data.id);

        if (videoRef.value && videoRef.value.paused) {
            try {
                await videoRef.value.play().catch(error => {
                    if (error.name !== 'AbortError') {
                        console.error('自动播放失败:', error);
                    }
                });
                isPlaying.value = false;
                emit('videoStateChange', true);
            } catch (error) {
                if (error.name !== 'AbortError') {
                    console.error('自动播放失败:', error);
                }
            }
        }
    } else {
        if (videoRef.value && !videoRef.value.paused) {
            videoRef.value.pause();
            isPlaying.value = true;
            emit('videoStateChange', false);
        }
    }
};

// 初始化视频
const initVideo = async () => {
    try {
        const video = videoRef.value;
        if (!video) return;

        video.currentTime = 0;
        isPlaying.value = false;

        if (props.autoPlay) {
            await video.play().catch(error => {
                if (error.name !== 'AbortError') {
                    console.error('自动播放失败:', error);
                }
            });
            isPlaying.value = false;
        }
    } catch (error) {
        if (error.name !== 'AbortError') {
            console.error('视频初始化失败:', error);
        }
    }
};

// 存储原始视口高度
let originalHeight = null;

// 设置视窗高度CSS变量
const setViewportHeight = () => {
    if (!originalHeight) {
        originalHeight = window.innerHeight;
    }
    document.documentElement.style.setProperty('--vh', `${originalHeight}px`);
};

// 监听视口大小变化
const handleResize = () => {
    // 只在非评论状态更新视口高度
    if (!isCommentOpen.value) {
        originalHeight = window.innerHeight;
        setViewportHeight();
    }
};

// 添加一个状态跟踪评论是否打开
const isCommentOpen = ref(false);

// 保存 IntersectionObserver 实例
let observer = null;

// 组件挂载时
onMounted(() => {
    initVideo();
    const observer = new IntersectionObserver(handleVisibilityChange, {
        threshold: 0.5
    });

    if (videoRef.value) {
        observer.observe(videoRef.value);
    }
    props.data.isLike ? imgLoveUrl.value = isSeleteVideoLike.selete : imgLoveUrl.value = isSeleteVideoLike.noselete
    props.data.isCollect ? imgCollectUrl.value = isSeleteVideoCollect.selete : imgCollectUrl.value = isSeleteVideoCollect.noselete
    onUnmounted(() => {
        if (videoRef.value) {
            observer.unobserve(videoRef.value);
        }
        observer.disconnect();

        if (controlsTimer) {
            clearTimeout(controlsTimer);
        }
    })
    // 初始化视窗高度
    setViewportHeight();
    window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
});

// 添加删除方法
const deleteItem = () => {
    emit('delete', props.data.id);
};
emitter.on('likeDetailSuccess', (data) => {
    console.log('监听到like')
    //   判断是否是当前的item
    if (props.data.id == data) {
        if (props.data.isLike) {
            props.data.likeNum--
            props.data.isLike = 0
            imgLoveUrl.value = isSeleteVideoLike.noselete
        } else {
            props.data.likeNum++
            props.data.isLike = 1
            imgLoveUrl.value = isSeleteVideoLike.selete
        }
    }
})
emitter.on('collectDetailSuccess', (data) => {
    //   判断是否是当前的item
    if (props.data.id == data) {
        if (props.data.isCollect) {
            props.data.collectNum--
            props.data.isCollect = 0
            imgCollectUrl.value = isSeleteVideoCollect.noselete
        } else {
            props.data.collectNum++
            props.data.isCollect = 1
            imgCollectUrl.value = isSeleteVideoCollect.selete
        }
    }
})

// 监听评论关闭事件
emitter.on('closeComment', () => {
    isCommentOpen.value = false;

    // 恢复滑动
    if (window.swiper) {
        window.swiper.allowTouchMove = true;
    }

    // 重新初始化 IntersectionObserver
    if (videoRef.value && observer) {
        observer.disconnect();
        observer = new IntersectionObserver(handleVisibilityChange, {
            threshold: 0.5
        });
        observer.observe(videoRef.value);
    }
});

</script>

<style lang="scss" scoped>
.swipeItemCon {
    position: fixed;
    /* 改为固定定位 */
    width: 100%;
    height: var(--vh, 100vh);
    /* 使用CSS变量 */
    background: #000;
    display: flex;
    top: 0;
    left: 0;
    z-index: 10;

    .videoCon {
        position: relative;
        width: 100%;
        height: 100%;
        transition: width 0.3s ease;

        .video {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;

            .fullscreenVideo {
                width: 100%;
                height: 100%;
                object-fit: contain;
                background: #000;
            }
        }

        .top-controls {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            padding: 64px 90px 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: linear-gradient(to bottom, rgba(0, 0, 0, 0.5), transparent);
            color: white;
            z-index: 2;

            .back-button {
                img {
                    width: 50px;
                    height: 50px;
                }
            }

            .material {
                flex: 1;
                display: flex;
                align-items: center;
                margin-left: 39px;

                .name {
                    margin-left: 18px;
                    font-size: 28px;
                }
            }
        }

        .play-icon {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 2;

            .control-icon {
                width: 60px;
                height: 60px;
                opacity: 0.8;
                transition: opacity 0.3s ease;
                cursor: pointer;

                &:hover {
                    opacity: 1;
                }
            }
        }

        .bottom-controls {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            padding: 0 102px;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
            color: white;

            .time {
                font-size: 24px;
                margin-bottom: 16px;
            }

            .progress-bar {
                margin-bottom: 38px;

                .progress-bg {
                    position: relative;
                    width: 100%;
                    height: 4px;
                    background: rgba(255, 255, 255, 0.3);
                    border-radius: 2px;

                    .progress {
                        height: 100%;
                        background: #fff;
                        border-radius: 2px;
                    }
                }
            }

            .match-buttons {
                display: flex;
                margin-bottom: 20px;
                justify-content: space-between;
                align-items: center;

                .match-left {
                    flex: 1;
                    display: flex;
                    align-items: center;
                    overflow: hidden;

                    .content {
                        margin-left: 10px;
                        font-size: 30px;
                        display: inline-block;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;

                    }

                    .match-img {
                        margin-left: 4px;
                        width: 54px;
                        height: 54px;
                    }

                    .my {
                        margin-left: 14px;
                        width: 56px;
                        height: 38px;
                    }
                }

                .match-right {
                    display: flex;
                    align-items: center;
                    justify-content: flex-end;
                    width: 400px;

                    .num {
                        font-size: 26px;
                        margin-right: 18px;
                    }

                    .vote {
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        width: 124px;
                        height: 44px;
                        background: rgba(26, 26, 26, 0.43);
                        border-radius: 4px;
                        font-size: 26px;
                        font-weight: 500;
                    }
                }


            }


            .interaction-buttons {
                display: flex;
                margin-bottom: 48px;
                justify-content: space-between;
                align-items: center;

                .button {
                    display: flex;
                    align-items: center;

                    .button-item {
                        display: flex;
                        align-items: center;
                        color: white;
                        font-size: 24px;
                        margin-right: 34px;

                        img {
                            width: 48px;
                            height: 48px;
                            margin-right: 4px;
                        }
                    }
                }

                .comment-input {
                    flex: 1;
                    font-size: 30px;

                    .inputBg {
                        display: flex;
                        align-items: center;
                        width: 500px;
                        height: 36px;
                        background: rgba(255, 255, 255, 0.44);
                        border-radius: 37px;
                        padding: 14px 36px;
                        margin-left: 44px;
                    }

                    input {
                        flex: 1;
                        background: transparent;
                        border: none;
                        color: white;
                        outline: none;

                        &::placeholder {
                            color: rgba(255, 255, 255, 0.8);
                        }
                    }

                }

                .detailBtn {
                    font-size: 28px;
                }

                .delete {
                    margin: 30px;

                }
            }


        }
    }
}
</style>