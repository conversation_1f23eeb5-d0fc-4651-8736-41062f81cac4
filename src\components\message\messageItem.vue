<template>
    <div class="item" @click.stop="gotoDetail(data.id)">
        <div class="left">
            <avatar :url="data.avatart" :userId="data.userId" type="msg"></avatar>
        </div>
        <div class="right">
            <div class="info">
                <div class="top">
                    <div class="name">{{ data.userName }}</div>
                    <div class="time">{{ ymdDianTime(data.createTime) }}</div>
                </div>
                <!-- likeType:1 动态 2 评论 3回复 -->
                <!-- {{ $t('index.msg.likeCon') }} -->
                <div class="msg" v-if="data.messageType == 'like' && data.likeType == 1">{{ $t('index.msg.likeCond') }}
                </div>
                <div class="msg" v-else-if="data.messageType == 'like' && data.likeType == 2">{{ $t('index.msg.likeConc') }}
                </div>
                <div class="msg" v-else-if="data.messageType == 'like' && data.likeType == 3">{{ $t('index.msg.likeConr') }}
                </div>
                <div class="msg" v-else-if="data.messageType == 'collect'">{{ $t('index.msg.collectCon') }}</div>
                <div class="msg" v-else-if="data.messageType == 'comment'">{{ $t('index.msg.commentCon') }}:{{
                    data.content
                    }}</div>
                <div class="msg" v-else-if="data.messageType == 'ait'">{{ $t('index.msg.aitCon') }}</div>
                <div class="msg" v-else> {{ $t('index.msg.replyCon') }}:{{ data.content }}</div>
                <!-- 谁回复了我 内容 -->
                <!-- {{$t('index.msg.likeCon') }} -->
                <!-- {{$t('index.msg.collectCon') }} -->
                <!-- {{$t('index.msg.commentCon') }} -->
                <!-- {{$t('index.msg.aitCon') }} -->
            </div>
        </div>
        <div class="image">
            <img class="avatar" v-if="data.image" :src="data.image" alt="">
        </div>
        <Detail v-if="isShowDetail" :data="data" type="msg" :isShowDetail="isShowDetail" :index="index"
            @closePopup="closePopup"></Detail>
    </div>
</template>
<script setup>
import { ymdDianTime } from "@/utils/time.js"
import { ref, reactive, onMounted, computed, defineAsyncComponent } from 'vue';
import Detail from "@/components/detail/index.vue"
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    },
    index: {
        type: Number,
        require: false,
        default: null
    },
    dynamicType: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: ''
    },
});
const tooltipType = ref('')
const isShowDetail = ref(false)
// 计算时间
const time = ((data) => {
    return ymdDianTime(data)
})
// 向父组件传递popup显示事件
const gotoDetail = (() => {
    console.log('点击进入详情')
    isShowDetail.value = true

})
const closePopup = () => {
    isShowDetail.value = false
}
onMounted(() => {

})
</script>
<style lang="scss" scoped>
@import '@/assets/css/detail/commentItem.scss';

.item {
    background: var(--fffColor);
    padding: 10px 14px 14px 10px;

    .right {
        border-bottom: none;

        .top {
            display: flex;
            align-items: center;
            margin-bottom: 6px;

            .name {
                margin-bottom: 0;
            }

            .time {
                margin-left: 16px;
                font-size: 24px;
                color: #666660;
                line-height: 35px;
            }
        }

        .msg {
            font-size: 24px;
            color: var(--mainTtileColor);
            line-height: 35px;
        }
    }

    .image {
        img {
            width: 80px;
            height: 80px;
        }
    }
}
</style>