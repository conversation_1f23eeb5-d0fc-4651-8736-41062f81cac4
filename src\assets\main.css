@import './base.scss';

* {
  padding: 0;
  margin: 0;
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

html,
body {
  font-weight: 400;
  font-family: SourceHanSansSC, SourceHanSansSC;
  font-size: 18px;
  overflow: hidden;
    height: 100%;
  /* 隐藏滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }
}


#app {
  width: 100%;
  height: 100%;
}

.container {
  width: 100%;
  height: 100%;
  /*  width: 100vh;
  height: 100vh;
  transform: rotate(90deg);
  transform-origin: 50vw 50vw; */

}

img {
  /* pointer-events:none; */
}
