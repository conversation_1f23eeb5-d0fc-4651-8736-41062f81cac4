.itemCon {
    height: 100%;
    margin: 16px 16px 16px 0;
    background-color: #FFFFFF;

    .top {
        display: flex;
        align-items: center;
        font-size: 26px;
        padding: 10px 20px 10px 10px;
        background: #F5F5F5;

        .avatar {
            width: 80px;
            height: 80px;
        }

        .info {
            flex: 1;
            margin-left: 10px;

            .name {
                color: #333333;
                line-height: 37px;
                min-height: 37px;
            }

            .time {
                font-size: 22px;
                color: #666660;
                line-height: 32px;
            }
        }
    }

    .content {
        padding: 12px 16px 22px;

        .topic {
            color: #425E81;
            font-size: 25px;
            display: inline-block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            /* 根据需要调整 */
            vertical-align: top;

            /* 根据需要调整 */
            span {
                padding-right: 10px;
            }
        }

        .text-box {
            font-size: 26px;
            color: #333333;
            line-height: 37px;
            word-wrap: break-word;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
            white-space: pre-wrap;
            /* 保持换行 */
        }

        .seeFile {
            margin-top: 16px;
            background: rgba(81, 149, 231, 0.1);
            display: flex;
            align-items: center;
            padding: 0 16px;
            font-size: var(--size_22);
            height: 42px;

            img {
                width: 30px;
                height: 30px;
            }

            .name {
                margin-left: 10px;
                flex: 1;
                color: var(--mainTtileColor);
                white-space: nowrap;
                /* 禁止文本换行 */
                overflow: hidden;
                /* 隐藏超出宽度的文本 */
                text-overflow: ellipsis;
                /* 在文本超出宽度时显示省略号 */
            }

            .size {
                color: #5195E7;
            }

        }

        .img {
            width: 100%;
            margin-top: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 200px; // 预设最小高度，避免布局跳动
            position: relative; // 为加载状态定位
            background-color: #f5f5f5; // 容器背景色
            border-radius: 8px; // 容器圆角

            img {
                max-width: 100%; // 改为100%，确保图片完整显示
                width: 100%; // 设置宽度为100%
                height: auto; // 保持宽高比
                display: block;
                object-fit: contain; // 确保图片完整显示，不被裁剪
                border-radius: 8px; // 图片圆角
                opacity: 0; // 初始透明，加载完成后显示
                transition: opacity 0.3s ease; // 添加加载过渡效果

                // 图片加载完成后显示
                &.image-loaded {
                    opacity: 1;
                }
            }

            // 图片加载占位符
            .image-placeholder {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                display: flex;
                align-items: center;
                justify-content: center;

                .loading-spinner {
                    width: 40px;
                    height: 40px;
                    border: 3px solid #e0e0e0;
                    border-top: 3px solid #5195E7;
                    border-radius: 50%;
                    animation: spin 1s linear infinite;
                }
            }

            // 加载动画
            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }
        }

        .videoList {
            width: 100%;
            margin-top: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;

            .videoPosterContainer {
                position: relative;
                /* 设置容器为相对定位 */
                // width: auto;
                max-width: 100%;
                /* 宽度自动调整 */
                height: 300px;

                /* 设置固定高度 */
                .videoPoster {
                    max-width: 100%;
                    // height: 300px;
                    // display: block;
                    // width: auto;
                    /* 根据需要调整宽度 */
                    height: 300px;
                }

                .videoBtn {
                    position: absolute;
                    top: 12px;
                    right: 12px;
                    width: 48px;
                    height: 48px;
                    border-radius: 50%;
                    background: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;

                    img {
                        width: 24px;
                        height: 24px;
                    }

                }
            }
        }

        .bottom {
            display: flex;
            align-items: center;
            font-size: 24px;
            color: #666666;
            height: 38px;
            margin-top: 22px;

            .bottomCon {
                flex: 1;
                display: flex;
                align-items: center;

                .bottomItem {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    margin-right: 44px;
                    vertical-align: middle;

                    img {
                        width: 38px;
                        height: 38px;
                        margin-right: 6px;
                    }
                }
            }


        }

        .like {
            display: -webkit-box;
            /* OLD - iOS 6-, Safari 3.1-6 */
            display: -moz-box;
            /* OLD - Firefox 19- (buggy) */
            display: -ms-flexbox;
            /* TWEENER - IE 10 */
            display: -webkit-flex;
            /* NEW - Chrome 21-28, Safari 6.1+ */
            display: flex;
            /* NEW - Modern Browsers */
            -webkit-box-align: center;
            /* OLD - iOS 6-, Safari 3.1-6 */
            -moz-box-align: center;
            /* OLD - Firefox 19- */
            -ms-flex-align: center;
            /* IE 10 */
            -webkit-align-items: center;
            /* Chrome 21-28, Safari 6.1+ */
            align-items: center;
            /* 标准语法 */
            // height: 44px;
            padding: 10px 0;
            font-size: 24px;
            color: #666666;
            background: #F5F5F5;
            margin-top: 12px;

            img {
                width: 32px;
                height: 32px;
                margin-left: 6px;
            }

            .likeList {
                overflow: hidden; //超出的文本隐藏
                display: -webkit-box;
                -webkit-line-clamp: 1; // 超出多少行
                -webkit-box-orient: vertical;

                .showSpace {
                    white-space: pre;
                }
                .like-item {
                    display: inline-flex;
                    align-items: center;
                    direction: ltr;
                }

                .item-text {
                    unicode-bidi: isolate;
                }

                .separator {
                    display: inline-block;
                    margin: 0 2px;
                }
            }

        }
    }

}