<template>
    <Tab :data="tabs" type="msg" @changeTab="changeTab"></Tab>
    <van-list class="messageCon" @load="changeMsgData" :immediate-check="true" :offset="1">
        <div v-if="!loading && msgData.length">
            <div v-for="(item, index) in msgData" :key="index">
                <MessageItem :dynamicType="countStore.loginRole" :type='type' :data="item" :index="index">
                </MessageItem>
            </div>
        </div>
        <div v-else-if="!loading" class="noComment">
            <Empty :title="$t('empty.message')" />
        </div>
    </van-list>
</template>
<script setup>
import { ref, onActivated, onMounted, onUnmounted, watch, defineAsyncComponent } from 'vue';
import { showToast } from 'vant';
const MessageItem = defineAsyncComponent(() =>
    import('@/components/message/messageItem.vue')
);
const Empty = defineAsyncComponent(() =>
    import('@/components/common/empty.vue')
);
const Tab = defineAsyncComponent(() =>
    import('@/components/index/tab.vue')
);
import emitter from '@/utils/mitt.js'
import { getmsgList, friendData } from "@/api/home.js"
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
const msgType = ref(1)
const pageNum = ref(1)
const pageSize = ref(10)
const msgData = ref([])
const total = ref(0)
const loading = ref(true)
const type = ref('')
const tabs = ref([{ id: 1, title: t('index.msg.likeCollectTab') }, { id: 2, title: t('index.msg.commentAitTab') }, { id: 3, title: t('index.msg.reply') }]);
// 添加请求标识符
const requestId = ref(0)
const props = defineProps({
    activeTab: {
        type: Number,
        require: false,
        default: 0
    },
})
watch(() => props.activeTab, (n, o) => {
    if (n = 6) {
        msgType.value = 1
        pageNum.value = 1
        getMsgList()
    }
})
// 消息栏
const changeTab = (tab) => {
    console.log(tab)
    msgType.value = tab
    pageNum.value = 1
    getMsgList()
}
// 更新消息数据
const changeMsgData = (data) => {
    console.log('sss')
    if (msgData.value.length) {
        if (msgData.value.length < total.value) {
            pageNum.value++
            getMsgList()
            console.log('更新数据')
        }
    }

}
var limitId = 0
const getMsgList = () => {
    console.log(msgType.value)
    if (pageNum.value == 1) {
        limitId = 0
    } else {
        limitId = msgData.value.length ? msgData.value[0].id : 0
    }
    const query = {
        messageType: msgType.value,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        limitId: limitId
    }
    
    // 增加当前请求的标识符
    const currentRequestId = ++requestId.value
    
    getmsgList(query)
        .then((res) => {
            // 如果不是最新的请求，则忽略这个响应
            if (currentRequestId !== requestId.value) {
                console.log('忽略过期的请求响应', currentRequestId, requestId.value)
                return
            }
            
            console.log(res)
            if (res.code === 200) {
                total.value = res.total
                // 获取消息数据成功
                emitter.emit('msgRead', msgType.value)
                if (res.rows) {
                    if (pageNum.value == 1) {
                        msgData.value = res.rows
                        console.log(msgData.value)
                    } else {
                        res.rows.forEach(item => {
                            msgData.value.push(item)
                        });

                    }
                }
            } else {
                showToast(t('toast.msgDataListFail'));
                if (pageNum.value == 1) {
                    msgData.value = []
                }
            }
            loading.value = false
        })
        .catch(function (error) {
            // 如果不是最新的请求，则忽略这个错误
            if (currentRequestId !== requestId.value) {
                console.log('忽略过期的请求错误', currentRequestId, requestId.value)
                return
            }
            
            console.log(error);
            loading.value = false
            showToast(t('toast.msgDataListFail'));
            if (pageNum.value == 1) {
                msgData.value = []
            }
        });
};
onActivated(() => {
    console.log('激活msg')
})
onMounted(()=>{
    getMsgList()
})
// 监听到更新列表
emitter.on('updateMessageList', () => {
    pageNum.value = 1
    msgData.value = []
    getmsgList()
})
onUnmounted(() => {
    emitter.off('updateMessageList')
})
</script>
<style scoped lang="scss">
.messageCon {
    flex: 1;
    margin-left: 7px;
    margin-right: 34px;
    overflow: auto;
}

.noComment {
    height: 100%;
    flex: 1;
}
</style>