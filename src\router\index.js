import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/components/Layout.vue'
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'layout',
      redirect:'/index',
      component: Layout,
      children:[
        {
          path: '/index',
          name: 'index',
          component: () => import('@/views/index/index.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/home',
          name: 'home',
          component: () => import('@/views/index/home.vue'),
          meta: {
            keepAlive: true,
          }
        },
        {
          path: '/reportOne',
          name: 'reportOne',
          component: () => import('@/views/report/reportOne.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/reportTwo',
          name: 'reportTwo',
          component: () => import('@/views/report/reportTwo.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/reportEnd',
          name: 'reportEnd',
          component: () => import('@/views/report/reportEnd.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/term/serve',
          name: 'term/serve',
          component: () => import('@/views/term/serve.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/match',
          name: 'match',
          component: () => import('@/views/match/index.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/matchDetail',
          name: 'matchDetail',
          component: () => import('@/views/match/detail.vue'),
          meta: {
            keepAlive: true,
          },
        },
        {
          path: '/video',
          name: 'video',
          component: () => import('@/views/video/index.vue'),
          meta: {
            keepAlive: false,
            dynamicKeepAlive: true
          },
        },
        
      ]
    },
  ]
})
router.beforeEach((to, from, next) => {
  if (to.path === '/reportTwo' && from.path === '/reportOne') {
    // 如果是从 report-one 回退到 report-one，缓存组件
    next()
  } else {
    next()
  }
})

export default router
