<template>
    <div class="item" @click="onItemClick">
        <div class="cover">
            <img class="coverImage" :src="data.voteType == '1' ? data.voteImageList[0] : data.voteVideoFrame" alt="">
            <div class="rank">
                <img v-if="data.voteTop == 1" src="@/assets/images/match/first.webp" alt="">
                <img v-if="data.voteTop == 2" src="@/assets/images/match/second.webp" alt="">
                <img v-if="data.voteTop == 3" src="@/assets/images/match/thirdS.webp" alt="">
            </div>
            <div class="video" v-if="data.voteType == '2'">
                <img src="@/assets/images/match/videoIcon.webp" alt="">
            </div>
        </div>
        <div class="card-info">
            <div class="info-title">{{ data.voteTitle }}</div>
            <div class="info-con">
                <div class="material">
                    <avatar :url="data.avatar" :userId="data.userIdStr"></avatar>
                    <div class="name">{{ data.nickName }}</div>
                </div>
                <Vote :data="data" :type="type" />
            </div>
        </div>
       
    </div>
</template>
<script setup>
import { defineProps, defineEmits ,defineAsyncComponent} from 'vue';
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
const Vote = defineAsyncComponent(() =>
    import('@/components/match/vote.vue')
)
// 接收父组件传递的props
const props = defineProps({
    data: {
        type: Object,
        required: true
    },
    type: {
        type: String,
        required: true
    }
});

// 定义要向父组件发出的事件
const emit = defineEmits(['item-click']);
// 卡片点击事件
const onItemClick = () => {
    emit('item-click', props.data,props.type);
};

</script>

<style lang="scss" scoped>
// 视频卡片样式
.item {
    width: calc(50% - 8px) !important; // 强制设置宽度为50%减去间距的一半
    position: relative;
    min-height: 80px;
    box-sizing: border-box;
    margin: 0 4px 16px 4px !important; // 统一设置左右margin为4px，底部16px
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden; // 确保内容不溢出
    transform: translateZ(0); // 为每个item启用硬件加速
    will-change: transform; // 优化变换性能

    .cover {
        position: relative;

        .coverImage {
            width: 100%;
            height: 347px;
            object-fit: cover; // 确保图片填满容器
        }

        .rank {
            position: absolute;
            top: 18px;
            left: 18px;

            img {
                width: 68px;
                height: 68px;
            }

        }

        .video {
            position: absolute;
            top: 18px;
            right: 18px;

            img {
                width: 46px;
                height: 46px;
            }
        }
    }

}

// 卡片底部信息
.card-info {
    padding: 15px 24px;

    .info-title {
        font-size: 28px;
        color: rgba(51, 51, 51, 0.9);
        margin-bottom: 4px;
    }

    .info-con {
        display: flex;
        justify-content: space-between;
        align-content: center;

        .material {
            display: flex;
            align-items: center;

            img {
                width: 60px;
                height: 60px;
            }

            .name {
                font-size: 26px;
                color: #333333;
                margin-left: 12px;
            }

            .my {
                margin-left: 14px;
                width: 56px;
                height: 38px;
            }


        }
    }
}
</style>
