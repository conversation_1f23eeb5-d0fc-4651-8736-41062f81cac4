<template>
    <div class="con">
        <div class="top">
            <img @click="back" src="@/assets/images/index/back (2).png" />
            <div class="title">Content Posting Agreement</div>
        </div>
        <div class="centerCon">
            <div class="con-detail-text">
                <div class="con-detail-text-subtitle">1. Content Posting</div>
                You may post original or authorized content, such as viewpoints, text, information, images, audio, and
                video, on the HAPMETA SOCIAL platform. Note that any content you post doesn't represent HAPMETA SOCIAL's
                views, perceptive, or policy, and you bear full responsibility for it. When posting content, you must
                comply with local laws and regulations, and you're prohibited from using HAPMETA SOCIAL to copy, post,
                spread, or store any violent, pornographic, political, bloody, or harmful information targeting minors,
                as well as any other information we deem inappropriate, including false information. <br>
                Users are not allowed to create, upload, copy, transmit, or spread false news or other information
                prohibited by local laws and regulations using new technologies and applications like deep learning,
                virtual reality, and generative AI, nor can they claim synthetic content as real or natural. When
                posting or spreading non - genuine information made via these new technologies, you must label it
                clearly. Otherwise, we have the right to take measures against the relevant content and accounts, such
                as adding identifiers, restricting, or banning them. <br>
                <div class="con-detail-text-subtitle">2. Content Information Authorization</div>
                Content you post or upload on HAPMETA SOCIAL (including but not limited to text, images, videos, and
                audio) must have a legal source, either owned by you or authorized.<br>
                By default, absent contrary evidence, you understand and agree to grant HAPMETA SOCIAL a free,
                irrevocable, non - exclusive, global license. This license allows HAPMETA SOCIAL to store, use,
                distribute, copy, modify, adapt, compile, publish, display, translate, perform your content, create
                derivative works, incorporate the content into other works in any known or future format, media, or
                technology, and authorize third parties to use the content in the same way. It also grants HAPMETA
                SOCIAL the right to take evidence, file complaints, or initiate lawsuits against infringements of your
                intellectual property.<br>
                To avoid ambiguity, this license includes the rights to use, copy, and display personal images,
                portraits, names, trademarks, brands, logos, and other promotional materials in your content.
                <div class="con-detail-text-subtitle">3. Infringement Complaints</div>
                We respect and protect legitimate rights like intellectual property, reputation, name, and privacy. You
                must ensure that content you post doesn't infringe upon any third - party rights. If HAPMETA SOCIAL
                receives a rights - holder's notice about infringing content, it may be removed.<br>
                If you think HAPMETA SOCIAL content infringes your rights, send a notice following the infringement
                complaint guidelines. Upon receiving a valid notice, we'll review it and take action against content
                preliminary deemed infringing. For content not deemed infringing, you'll be informed. If you still
                believe infringement exists, provide additional evidence in a new notice. Within the maximum limits
                allowed by applicable law, data stored on HAPMETA SOCIAL's servers is the only valid evidence of user
                activity.
            </div>
        </div>
    </div>

</template>
<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
const router = useRouter()
const back = () => {
    router.go(-1)
}
</script>
<style lang="scss" scoped>
.con {
    height: 100vh;
    display: flex;
    flex-direction: column;
    margin: 0 66px;
    .top {
        font-size: var(--size_36);
        color: var(--mainTtileColor);
        line-height: 48px;
        margin: 26px 0 38px;
        display: flex;

        .title {
            flex: 1;
            text-align: center;
        }

        img {
            width: 40px;
            height: 40px;
        }
    }

    .centerCon {
        flex: 1;
        overflow: auto;

        .con-detail-text {
            font-size: 22px;
            color: var(--mainTtileColor);
            line-height: 34px;
            text-shadow: inset 0px 1px 3px rgba(0, 0, 0, 0.5);
            border-top: 1px solid #D9D9D9;
            padding-top: 47px;

            .con-detail-text-subtitle {
                font-size: 26px;
                font-weight: 600;
                color: rgba(51, 51, 51, 0.9);
                line-height: 32px;
                margin: 14px 0;
            }
        }
    }
}
</style>