<template>
    <div class="tab">
        <van-tabs v-model:active="active" shrink @change="changeTab" :line-width="lineWidth">
            <van-tab v-for="(tab, index) in data" :key="index" :dot="tab.isRead">
                <template #title>
                    <span :ref="(el) => (tabRefs[index] = el)">{{ tab.title }}</span>
                </template>
            </van-tab>
        </van-tabs>
        <div class="popover">
            <van-popover v-model:show="showPopover" :actions="popoverData" @select="onSelect">
                <template #reference>
                    <div class="more" v-if="selectPopover">
                        <div>{{ selectPopover }}</div>
                        <img :class="showPopover ? 'turn' : ''" src="@/assets/images/video/tabMore.webp" />
                    </div>
                </template>
            </van-popover>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import emitter from '@/utils/mitt.js'
const currentTab = ref(0);
const $emit = defineEmits(['changeTab', 'changePopover'])
import { useI18n } from "vue-i18n";
const { t } = useI18n();
const commentAitTab = ref(false)
const reply = ref(true)
const active = ref(0)
const selectPopover = ref()
const props = defineProps({
    data: {
        type: Array,
        require: false,
        default: []
    },
    type: {
        type: String,
        require: false,
        default: ''
    },
    popoverData: {
        type: Array,
        require: false,
        default: []
    }
})
const tabRefs = ref([]);
const lineWidth = ref(0);
const showPopover = ref(false);
const onSelect = (data) => {
    console.log(data)
    selectPopover.value = data.text
    $emit('changePopover', data.id)
};
watch(active, async (newVal) => {
    await nextTick(); // 等待DOM更新
    if (tabRefs.value[newVal]) {
        const textWidth = tabRefs.value[newVal].offsetWidth;
        lineWidth.value = textWidth;
    }
});
// 初始化首次计算
nextTick(() => {
    if (tabRefs.value[0]) {
        lineWidth.value = tabRefs.value[0].offsetWidth;
    }
});
const changeTab = (index) => {
    console.log(index)
    currentTab.value = index;
    if (index == 0) {
        $emit('changeTab', 1)
    } else if (index == 1) {
        $emit('changeTab', 2)
    } else if (index == 2) {
        $emit('changeTab', 3)
    }

};
emitter.on('msgRead', (data) => {
    // console.log(props.data.value[currentTab.value])
    // console.log(data)
    if (data == 1) {
        countStore.noReadMessageNum = countStore.noReadMessageNum - countStore.likeAndcollectNum
        countStore.likeAndcollectNum = 0
    } else if (data == 2) {
        countStore.noReadMessageNum = countStore.noReadMessageNum - countStore.aitAndConntentNum
        countStore.aitAndConntentNum = 0
    } else {
        countStore.noReadMessageNum = countStore.noReadMessageNum - countStore.replyNum
        countStore.replyNum = 0
    }
    if (props.data[currentTab.value].isRead) {
        props.data[currentTab.value].isRead = false
    }
})
emitter.on('menuChange', () => {
    console.log('改变')
    active.value = 0
    selectPopover.value = t('index.menuItems.New')
})
onMounted(() => {
    if (props.type == 'msg') {
        props.data.forEach(item => {
            if (item.id == 1) {
                item.isRead = countStore.likeAndcollectNum ? true : false
            } else if (item.id == 2) {
                item.isRead = countStore.aitAndConntentNum ? true : false
            } else {
                item.isRead = countStore.replyNum ? true : false
            }
        });
    } else {
        selectPopover.value = props.popoverData[0].text
    }

})
onUnmounted(() => {
    emitter.off('msgRead')
    emitter.off('menuChange')
})
</script>

<style scoped lang="scss">
.tab {
    margin-left: 7px;
    margin-right: 34px;
    margin-top: 30px;
    position: relative;

    .popover {
        position: absolute;
        right: 0;
        bottom: 0;
        height: 48px;

        .more {
            height: 48px;
            display: flex;
            align-items: center;
            margin-right: 50px;

            img {
                width: 16px;
                height: 16px;
                margin-left: 6px;

            }

            .turn {
                transform: scaleY(-1);
            }
        }

    }
}

:deep(.van-tab) {
    font-size: 24px;
    font-weight: 500;
    color: #666666;
    line-height: 36px;
    padding: 0 64px;

}

:deep(.van-tabs--line .van-tabs__wrap) {
    height: 48px;
}

:deep(.van-tab--active) {
    color: #318DE9;
    font-size: 24px;
    font-weight: 500;
}

:deep(.van-badge) {
    width: 20px;
    height: 20px;
    background: #F64040;
}

:deep(.van-badge--top-right) {
    top: 5px;
    right: -16px;
}

:deep(.van-popover__wrapper) {
    font-size: 22px;
    color: #666666;
    line-height: 32px;
}

:deep(.van-popover__action) {
    width: 100%;
    font-size: 22px;
    color: #666666;
    line-height: 32px;
}

// .van-tabs__line {
//   width: auto !important;
//   left: 50%;
//   transform: translateX(-50%);
//   padding: 0 8px; /* 根据实际文字边距调整 */
// }
</style>
