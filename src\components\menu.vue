<template>
    <div class="menu">
        <div class="memuCon">
            <div class="menuItem otherMenu" v-if="type == 'detail'">
                <img @click="back" src="@/assets/images/index/back.png">
            </div>
            <div class="menuItem" v-else>
                <a href="uniwebview://close">
                    <img src="@/assets/images/index/back.png">
                </a>
                <span>{{ $t('index.title') }}</span>
            </div>
            <van-sidebar v-model="active" @change="changeMenu">
                <van-sidebar-item v-for="item in menu" :key="item.url">
                    <template #title>
                        <div class=" menuItemCon" v-if="item.name == 'Messages'"> <div> {{ $t('index.menuItems.' +
                            item.name) }}</div>
                            <div class="badge"
                                v-if="countStore.noReadMessageNum > 0 && countStore.noReadMessageNum < 99">{{
                                    countStore.noReadMessageNum }}</div>
                            <div class="badge" v-else-if="countStore.noReadMessageNum > 99">99+</div>
                        </div>
                        <div class="menuItemCon" v-else> <div>
                                {{ $t('index.menuItems.' + item.name) }}
                        </div>
                        </div>
                    </template>
                </van-sidebar-item>
            </van-sidebar>
        </div>
        <div class="menuSh"></div>
    </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import menuList from "@/utils/menu.js"
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import { useRouter } from 'vue-router';
const router = useRouter()
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import emitter from '@/utils/mitt.js'
import _ from 'lodash';
const props = defineProps({
    type: {
        type: String,
        require: false,
        default: ''
    },
})
const menu = ref([])
const active = ref('0')
const activeUrl = ref(0)
const tabStartTime = ref(null)
watch(() => activeUrl.value, (n, o) => {
    if (n == 4) {
        // 开始计时
        tabStartTime.value = Date.now()
    } else if (o == 4 && n !== 4) {
        // 结束计时
        // 计算停留时间
        const stayTime = (Date.now() - tabStartTime.value) / 60000;
        // 向下取整停留时间（不保留小数点）
        const roundedStayTime = stayTime > 1 ? Math.round(stayTime) : 1;
        ta.track('stayTime', { stayTime: roundedStayTime })
        tabStartTime.value = null
    }
})
const back = () => {
    // console.log('首页')
    localStorage.setItem('homeScrollPosition', 0);
    router.go(-1)
    emitter.emit('homeBack')
}
// 定义组件的事件
const emits = defineEmits(['changeMenu'])
const changeMenu = (key, keyPath) => {
    // console.log(menu.value[key].url)
    activeUrl.value = menu.value[key].url
    emits('changeMenu', menu.value[key].url)
}
const getRequest = () => {
    const url = window.top.location.href;
    const params = {};
    url.replace(/[?&]+([^=&]+)=([^&]*)/gi, function (m, key, value) {
        params[key] = decodeURIComponent(value);
    });
    console.log(params); // 输出包含查询参数键值对组成的对象
    if (params.roleId) {
        //判断登录id和要查看的id是否是同一个 roleId seeUserAvatarId
        if (params.seeUserAvatarId) {
            countStore.loginRole = 'other'
            activeUrl.value = 1
        } else {
            countStore.loginRole = 'oneself'
            activeUrl.value = 5
        }
        const roles = countStore.loginRole
        console.log(countStore.loginRole)
        menu.value = menuList.filter((item) => item.roles.includes(roles))
    }
}
onMounted(() => {
    if (props.type == 'detail') {
        console.log(props.type)
        menu.value = menuList.filter((item) => item.roles.includes(props.type))
    } else {
        // 获取url传参
        getRequest()
    }

})

</script>
<style scoped lang="scss">
@import '@/assets/css/index/menu.scss';
</style>