<template>
    <div>
        <div class="con">
            <div class="top">
                <img @click="back" src="@/assets/images/index/back (2).png" />
                <div class="title">{{ title }}</div>
            </div>
            <div class="groupbox">
                <van-radio-group v-model="radio" shape="round" @change="onChange">
                    <div class="item" v-for="item in dataList" :key="item.id">
                        <div class="laberTop">
                            <div class="name">{{ item.title }}</div>
                            <van-radio :name="item.id"></van-radio>
                        </div>
                        <div class="detail" v-if="radio.includes(item.id)">{{ item.info }}</div>
                    </div>
                </van-radio-group>
            </div>
            <div class="publish">
                <div class="btn" @click="publish">{{ $t('report.next') }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import {
    ref, watch, onMounted, nextTick, onActivated, onUnmounted, onDeactivated
} from 'vue';
import emitter from '@/utils/mitt.js'
import { useRoute, useRouter } from 'vue-router';
import { reportList } from "@/assets/js/report.js"
import { showToast } from 'vant';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
const route = useRoute();
const router = useRouter()
const data = ref({})
const radio = ref('');
const title = ref('')
const dataList = ref()
onMounted(() => {
    console.log('reportOne页面挂载');
    // 防止外部滚动事件影响
    document.body.style.overflow = 'auto';
    
    // 如果是从视频页面来的，确保视频页面的滚动事件不会影响举报页面
    if (route.query.fromPath && route.query.fromPath.includes('/video')) {
        console.log('从视频页面进入举报页面，阻止事件冒泡');
        
        // 添加事件拦截器
        const preventPropagation = (e) => {
            e.stopPropagation();
        };
        
        document.querySelector('.con')?.addEventListener('wheel', preventPropagation);
        
        // 组件卸载时移除事件拦截器
        onUnmounted(() => {
            document.querySelector('.con')?.removeEventListener('wheel', preventPropagation);
            emitter.off('enterReport');
        });
    }
})
// 激活钩子函数
onActivated(() => {
    console.log('reportOne页面激活');
    // 调用举报方法并赋值
    dataList.value = reportList(countStore.language)
    // 在这里执行你的代码
    console.log('route', route.query);
    data.value = route.query
    // 根据举报类型 显示title
    if (data.value.reportType == 'dynamic') {
        const str = t('report.reportOneTitleAfterD');
        const newStr = str.replace('%name%', data.value.name);
        title.value =newStr
        // title.value = t('report.reportOneTitleBefore') + data.value.name + t('report.reportOneTitleAfterD')
    } else if (data.value.reportType == 'comment') {
        const str = t('report.reportOneTitleAfterC');
        const newStr = str.replace('%name%', data.value.name);
        title.value =newStr
        // title.value = t('report.reportOneTitleBefore') + data.value.name + t('report.reportOneTitleAfterC')
    } else {
        const str = t('report.reportOneTitleAfterR');
        const newStr = str.replace('%name%', data.value.name);
        title.value =newStr
        // title.value = t('report.reportOneTitleBefore') + data.value.name + t('report.reportOneTitleAfterR')
    }
})

// 失活钩子函数
onDeactivated(() => {
    console.log('reportOne页面失活');
})

emitter.on('enterReport', () => {
    console.log('初始化')
    // 初始化
    radio.value = ''
})
const onChange = (values) => {
    console.log(values); // 这里的values是当前所有勾选的复选框的name组成的数组
    console.log(radio.value)
};
const back = () => {
    router.go(-1);
    emitter.emit('homeBack');
};
// 点击下一步
const publish = () => {
    console.log(radio.value)
    if (radio.value) {
        // 判断选中的有没有children
        if (radio.value == '5' || radio.value == '6' || radio.value == '9') {
            // 直接跳转到最后一步
            const listData = dataList.value[radio.value]
            const newData = {
                reportId: data.value.reportId,
                reportType: data.value.reportType,
                toComplainUserId: data.value.toComplainUserId,
                name: data.value.name,
                menuId: listData.menuId,
                title: listData.title,
                backNum: 2,
            }
            router.push({ path: '/reportEnd', query: newData })
        } else {
            const listData = dataList.value[radio.value]
            // console.log(JSON.stringify(listData))
            const newData = {
                reportId: data.value.reportId,
                reportType: data.value.reportType,
                toComplainUserId: data.value.toComplainUserId,
                name: data.value.name,
                dataList: JSON.stringify(listData),
                backNum: 3,
            }
            router.push({ path: '/reportTwo', query: newData })
        }
    } else {
        showToast(t('toast.nextInfo'));
    }
}
// router.beforeEach((to, from, next) => {
//   if (from.path === '/reportTwo') {
//     console.log(router.currentRoute.value.query.oneData)
//     // 当从下一页回退到此页时，恢复selectedOption的值
//     radio.value = router.currentRoute.value.query.oneData || null;
//     console.log(radio.value)
//   }
//   next();
// });
onUnmounted(() => {
    emitter.off('enterReport')
})
</script>
<style></style>
<style lang="scss" scoped>
@import '@/assets/css/common/reportCheckbox.scss';
</style>