.information {
    margin-bottom: 16px;
    background: #FFFFFF;
    height: 98px;
    display: flex;
    align-items: center;
    padding: 5px 30px 7px 8px;
    color: var(--mainTtileColor);

    .right {
        margin-left: 10px;
        flex: 1;

        .name {
            font-weight: var(--weight5);
            font-size: var(--size_28);
            line-height: 40px;
        }
    }

    .button {
        margin-top: 8px;
        display: flex;
        height: 50px;
        line-height: 50px;

        .buttonItem {
            margin-left: 20px;
            padding: 0 30px;
            // width: 226px;
            height: 100%;
            font-size: var(--size_22);
            background: #ECF0F6;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;

            .number {
                padding-left: 10px;
                color: #348EE9;
            }
        }

        .btn {
            margin-left: 20px;
            height: 50px;
            line-height: 50px;
            background: #5195E7;
            border-radius: 4px;
            color: #FFFFFF;
            padding: 0 26px;
        }
    }

}