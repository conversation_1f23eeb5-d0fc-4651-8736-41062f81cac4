<template>
    <div class="vote" :class="[className]">
        <div class="num">{{ $t('match.voteNum', { num: data.voteCount }) }}</div>
        <div class="vote-btn afterVote" v-if="data.isVote == '1'">{{ $t('match.voted') }}</div>
        <div class="vote-btn" v-else @click.stop="onVote">{{ $t('match.vote') }}</div>
    </div>
</template>
<script setup>
import { defineProps, defineEmits } from 'vue';
import { voteWorks } from '@/api/match';
import { showToast } from 'vant';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import emitter from '@/utils/mitt';
// 接收父组件传递的props
const props = defineProps({
    data: {
        type: Object,
        required: true
    },
    type: {
        type: String,
        required: true
    },
    className: {
        type: String,
        required: false,
        default: ''
    }
});
const onVote = async () => {
    console.log('投票');
    try {
        const res = await voteWorks({
            contestId: props.data.id,
            userId: props.data.userIdStr
        });
        if (res.code == 200) {
            props.data.isVote = '1';
            props.data.voteCount++;
            showToast(t('match.voteSuccess'));
            // 判断是否是其他作品 如果是其他刷新列表
            if (props.type == 'other') {
                emitter.emit('refurbishWorkOtherList');
            }
        } else {
            showToast(t('match.voteFail'));
        }
    } catch (error) {
        showToast(t('match.voteFail'));
        console.error('投票失败:', error);
    }
}


</script>
<style lang="scss" scoped>
.vote {
    display: flex;
    align-items: center;

    .num {
        font-size: 24px;
        color: #666666;
        padding-right: 18px;
    }

    .vote-btn {
        width: 124px;
        height: 44px;
        background: rgba(77, 166, 255, 0.18);
        border-radius: 4px;
        color: #2C85DE;
        font-size: 26px;
        font-weight: 500;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .afterVote {
        background: rgba(141, 141, 141, 0.18);
        color: #808080;
    }
}

.detailVote {
    justify-content: flex-end;
    padding: 8px 24px;
    border-top: 1px solid #E5E5E5;

    .num {
        color: rgba(51, 51, 51, 0.72);
    }
}

.videoVote {
    justify-content: flex-end;
    width: 400px;

    .vote-btn {
        background: rgba(26, 26, 26, 0.43);
        color: #fff;
    }
    .afterVote {
        background: rgba(141, 141, 141, 0.18);
        color: #808080;
    }
}
</style>
