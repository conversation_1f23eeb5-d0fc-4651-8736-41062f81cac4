<template>
    <!-- 内容区域 -->
    <div class="content-container" ref="scrollContainer" :style="scrollBehaviorStyle">
        <!-- 视频卡片网格 -->
        <van-list v-show="!initialLoading" class="vanList" ref="waterfallList" v-model:loading="loading"
            :finished="finished" @load="changeWaterfallData" :offset="300" :immediate-check="true">
            <div v-if="videoList.length" class="masonry-wrapper">
                <div v-masonry="masonryId" transition-duration="0" item-selector=".item">
                    <div v-masonry-tile v-for="(item, index) in videoList" :key="index">
                        <match-item :item="item" :status="getStatusByActiveTab()" @item-click="videoCardClick" />
                    </div>
                </div>
            </div>
            <!-- 无数据提示，仅当非初始加载且无数据时显示 -->
            <div v-if="!videoList.length && !loading && !initialLoading" class="no-data">
                <Empty :title="$t('empty.content')" />
            </div>
        </van-list>
        <!-- 初始加载中显示骨架屏 -->
        <skeleton v-if="initialLoading" />
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed, onActivated, inject, defineAsyncComponent } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import _ from 'lodash';
import emitter from '@/utils/mitt';
import { showToast } from 'vant';
import Skeleton from '@/components/common/skeleton.vue'; // 导入骨架屏组件
import MatchItem from '@/components/match/matchItem.vue'; // 导入匹配项组件
import matchService from '@/services/matchService'; // 导入 matchService
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
const Empty = defineAsyncComponent(() =>
    import('@/components/common/empty.vue')
);

// 接收父组件传递的props
const props = defineProps({
    activeTab: {
        type: Number,
        default: 1
    },
    mainTab: {
        type: Number,
        default: 1
    },
    type: {
        type: String,
        default: 'normal' // 默认为普通赛事列表，可选值：normal, my
    }
});

// 定义要向父组件发出的事件
const emit = defineEmits(['scroll-position-saved']);

const $redrawVueMasonry = inject('redrawVueMasonry');
const router = useRouter();
const route = useRoute();

// 生成唯一的瀑布流标识符
const generateUniqueId = () => {
    const timestamp = Date.now();
    const random = Math.floor(Math.random() * 10000);
    return `match-masonry-${timestamp}-${random}`;
};

// 为当前组件实例创建一个唯一的瀑布流标识符
const masonryId = ref(generateUniqueId());
console.log('生成matchList瀑布流标识符:', masonryId.value);

// van-list相关状态
const loading = ref(false);
const finished = ref(false);
const scrollContainer = ref(null);
const waterfallList = ref(null);
// 初始加载状态标记，区分初始加载和后续加载
const initialLoading = ref(true);

// 视频列表数据
const videoList = ref([]);

// 存储滚动位置
const savedScrollPosition = ref(0);

// 控制滚动行为
const isInstantScroll = ref(false);
const scrollBehaviorStyle = computed(() => {
    return isInstantScroll.value
        ? { scrollBehavior: 'auto' }
        : { scrollBehavior: 'smooth' };
});

// 改进的重新布局函数，只针对当前瀑布流实例
const relayoutMasonry = (delay = 300) => {
    // 延迟执行以确保DOM已更新
    setTimeout(() => {
        // 先触发resize事件，但仅针对当前组件
        if (scrollContainer.value) {
            // 创建并分发一个自定义事件，而不是使用全局resize事件
            const customEvent = new CustomEvent('masonry-resize', {
                detail: { masonryId: masonryId.value }
            });
            scrollContainer.value.dispatchEvent(customEvent);
        }

        // 然后使用vue-masonry的重绘方法，但仅针对当前瀑布流实例
        if (typeof $redrawVueMasonry === 'function') {
            $redrawVueMasonry(masonryId.value);
            console.log(`触发瀑布流[${masonryId.value}]重新布局`);
        }
    }, delay);
};

// 视频卡片点击
const videoCardClick = (item) => {
    console.log('视频卡片点击:', item);

    // 保存当前滚动位置
    if (scrollContainer.value) {
        savedScrollPosition.value = scrollContainer.value.scrollTop;
        console.log('保存滚动位置:', savedScrollPosition.value);

        // 向父组件发出事件，传递保存的滚动位置
        emit('scroll-position-saved', savedScrollPosition.value);
    }

    router.push({
        path: '/matchDetail',
        query: {
            id: item.id || '1'
        }
    });
    if (countStore.deviceType == 'Web') {
    } else {
        // 通知打开竖屏
        window.location.href = "uniwebview://changeToPortrait";
    }
};

// 优化后的无感恢复滚动位置函数
const restoreScrollPositionOptimized = (position = savedScrollPosition.value) => {
    console.log('开始优化的无感恢复滚动位置');

    // 启用即时滚动模式（禁用平滑滚动）
    isInstantScroll.value = true;

    // 立即恢复滚动位置
    if (scrollContainer.value) {
        // 使用RAF确保在下一帧渲染前设置滚动位置
        requestAnimationFrame(() => {
            scrollContainer.value.scrollTop = position;
            console.log('立即恢复滚动位置:', position);

            // 在滚动位置恢复后，以最小延迟触发重新布局
            relayoutMasonry(50);

            // 恢复普通滚动模式
            setTimeout(() => {
                isInstantScroll.value = false;
            }, 100);
        });
    }
};

// 提供给父组件的方法
defineExpose({
    restoreScrollPosition: restoreScrollPositionOptimized,
    relayoutMasonry
});

// 根据二级标签获取状态
const getStatusByActiveTab = () => {
    switch (props.activeTab) {
        case 0:
            return 0; // 待开始
        case 1:
            return 2; // 进行中
        case 2:
            return 3; // 已结束
        default:
            return 0; // 默认待开始
    }
};

// 加载数据的函数 - 使用 matchService
const loadData = async (pageNum = 1) => {
    // 如果是我的赛事列表，不需要检查 mainTab
    // 如果是普通赛事列表，需要检查 mainTab
    if (props.type !== 'my' && !props.mainTab) return;

    const status = getStatusByActiveTab();

    // 如果是初始加载，显示加载状态
    if (pageNum === 1) {
        initialLoading.value = true;
    }

    loading.value = true;

    try {
        let result;

        // 根据类型决定调用哪个函数
        if (props.type === 'my') {
            // 使用 fetchMyMatchList 获取我的赛事列表
            result = await matchService.fetchMyMatchList(
                status,
                pageNum,
                10,
                pageNum === 1 // 仅在第一页时强制刷新
            );
        } else {
            // 使用 fetchMatchList 获取普通赛事列表
            result = await matchService.fetchMatchList(
                props.mainTab,
                status,
                pageNum,
                10,
                pageNum === 1 // 仅在第一页时强制刷新
            );
        }

        // 更新列表数据
        if (pageNum === 1) {
            videoList.value = result.list;
        } else {
            videoList.value = [...videoList.value, ...result.list];
        }

        // 更新加载完成状态
        finished.value = result.finished;

        // 重新布局瀑布流
        nextTick(() => {
            relayoutMasonry(100);
        });
    } catch (error) {
        console.error('加载比赛列表数据失败:', error);
    } finally {
        loading.value = false;
        initialLoading.value = false;
    }
};

// 瀑布流加载更多数据 - 使用 matchService
const changeWaterfallData = _.debounce(() => {
    console.log('加载更多数据触发');

    // 如果是初始加载或已经加载完成，不发送请求
    if (initialLoading.value || finished.value || loading.value) {
        console.log('加载被阻止：',
            initialLoading.value ? '初始加载中' :
                finished.value ? '已全部加载' : '正在加载中');
        return;
    }

    // 计算当前页码（基于已加载项目数量）
    const currentPage = Math.ceil(videoList.value.length / 10) + 1;

    // 加载下一页数据
    loadData(currentPage);
}, 1000, { leading: true, trailing: false })

// 监听自定义事件，处理瀑布流布局
const handleMasonryResize = (event) => {
    // 只处理与当前瀑布流实例相关的事件
    if (event.detail && event.detail.masonryId === masonryId.value) {
        if (typeof $redrawVueMasonry === 'function') {
            $redrawVueMasonry(masonryId.value);
        }
    }
};

// 在组件挂载后初始化
onMounted(() => {
    console.log('matchList组件挂载');
    
    // 添加自定义事件监听
    if (scrollContainer.value) {
        scrollContainer.value.addEventListener('masonry-resize', handleMasonryResize);
    }
    
    // 初始化加载数据
    loadData(1);
    
    // 添加窗口大小变化监听，但使用防抖函数避免频繁触发
    const handleResize = _.debounce(() => {
        relayoutMasonry();
    }, 200);
    window.addEventListener('resize', handleResize);
    
    // 保存清理函数，以便在组件卸载时调用
    onUnmounted(() => {
        window.removeEventListener('resize', handleResize);
        if (scrollContainer.value) {
            scrollContainer.value.removeEventListener('masonry-resize', handleMasonryResize);
        }
    });
});

// 当组件从缓存中被激活时调用
onActivated(() => {
    console.log('matchList组件激活');
    
    // 重新生成瀑布流标识符，确保唯一性
    masonryId.value = generateUniqueId();
    console.log('重新生成matchList瀑布流标识符:', masonryId.value);
    
    // 延迟一点时间后重绘瀑布流，确保DOM已经准备好
    setTimeout(() => {
        relayoutMasonry();
    }, 200);
});

// 监听参数变化
watch(
    () => ({
        categoryId: props.mainTab,
        status: getStatusByActiveTab(),
        type: props.type
    }),
    (newParams, oldParams) => {
        console.log('参数变化:', newParams, oldParams);

        // 只有当值真正变化时才重新加载
        if (
            !oldParams || // 初始化
            newParams.categoryId !== oldParams.categoryId ||
            newParams.status !== oldParams.status ||
            newParams.type !== oldParams.type
        ) {
            console.log('参数变化触发数据重载');

            // 重置状态
            finished.value = false;
            videoList.value = [];

            // 设置初始加载状态
            initialLoading.value = true;

            // 重新加载数据
            loadData(1);
        }
    },
    { deep: true }
);

// 单独监听 activeTab 变化，用于日志记录
watch(() => props.activeTab, (newTab, oldTab) => {
    console.log('二级标签切换，从', oldTab, '到', newTab);
}, { immediate: false });

// 单独监听 mainTab 变化，用于日志记录
watch(() => props.mainTab, (newTab, oldTab) => {
    console.log('主标签切换，从', oldTab, '到', newTab);
}, { immediate: false });
</script>

<style lang="scss" scoped>
// 内容区域样式
.content-container {
    flex: 1;
    // height: 100%;
    padding: 20px 78px;
    overflow: auto;
    -webkit-overflow-scrolling: touch; // 增加iOS滚动支持
    position: relative;
    will-change: scroll-position; // 提示浏览器优化滚动性能
    display: flex;
    flex-direction: column;

    // 添加性能优化属性
    transform: translateZ(0); // 强制启用硬件加速
    backface-visibility: hidden; // 防止渲染闪烁
    perspective: 1000; // 改善3D性能
    contain: layout style paint; // 优化渲染性能

    .no-data {
        height: 100%;
        text-align: center;
        color: #999;
    }
}

// 瀑布流容器
.vanList {
    width: 100%;
    height: 100%;
    // padding-bottom: 40px; // 底部添加一些padding，防止内容被遮挡
}

.masonry-wrapper {
    width: 100%;
    // padding-top: 10px; // 顶部添加一些padding
    min-height: 100%; // 确保最小高度为100%
    contain: layout style paint; // 优化渲染性能
}

// 确保v-masonry容器正确显示
[v-masonry] {
    width: 100% !important;
    margin: 0 auto;
    transform: translateZ(0); // 强制启用硬件加速
    backface-visibility: hidden; // 防止渲染闪烁
    perspective: 1000; // 改善3D性能
    will-change: transform; // 提示浏览器优化变换
    contain: layout style paint; // 优化渲染性能
}
</style>
