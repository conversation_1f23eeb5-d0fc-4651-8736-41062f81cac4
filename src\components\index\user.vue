<template>
    <div class="information">
        <div v-if="data.icon">
            <avatar :url="data.icon" :userId="data.roleId"></avatar>
        </div>
        <div v-else>
            <van-skeleton-image />
        </div>
        <div class="right">
            <div class="name">{{ data.name }}</div>
        </div>
        <div class="button">
            <!-- <a  :href="chatPaneLink"> </a> -->
            <div v-if="data.isFriend == 1 && type == 'home'" class="btn" @click="userInfo('friendsLink')">{{
                $t("index.user.chat") }}
            </div>
            <div v-else-if="data.isFriend == 2 && type == 'home'" class="btn" @click="userInfo('addFriend')">{{
                $t("index.user.addFriends") }}</div>
            <div class="buttonItem"><span class="text">{{ $t("index.user.praised") }}</span><span class="number">{{
                data.likeNum }}</span></div>
            <div class="buttonItem"><span class="text">{{ $t("index.user.collect") }}</span><span class="number">{{
                data.collectNum }}</span>
            </div>
        </div>
        <friendRequest v-if="dialogFriendRequest" :dialogFriendRequest="dialogFriendRequest" :data="data"
            :avatarId="avatarId" @dialogClose="dialogClose" @confirm="confirm">
        </friendRequest>
    </div>
</template>
<script setup>
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { ref, onMounted, defineAsyncComponent, watch } from "vue";
import emitter from '@/utils/mitt.js'
import { user } from "@/api/home.js"
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
const friendRequest = defineAsyncComponent(() =>
    import('@/components/dialog/friendRequest.vue')
);
import { useRouter } from 'vue-router';
const router = useRouter()
const dialogFriendRequest = ref(false)
const chatPaneLink = ref('')
const avatarId = ref('')
const props = defineProps({
    data: {
        type: Object,
        require: false,
        default: {}
    },
    roleId: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: ''
    }
})
const addFriend = () => {
    dialogFriendRequest.value = true
}
const dialogClose = () => {
    dialogFriendRequest.value = false
}
const confirm = () => {
    dialogFriendRequest.value = false
}
const friendsLink = () => {
    window.location.href = chatPaneLink.value;
}
const emits = defineEmits(['uploadData'])
const userInfo = (type) => {
    const userType = 2
    const userId = props.data.roleId
    user(userType, userId)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                // userData.value = res.data
                console.log(props.data.isFriend)
                if (props.data.isFriend == res.data.isFriend) {
                    if (type == 'addFriend') {
                        addFriend()
                    } else {
                        friendsLink()
                    }
                } else {
                    // 通知父组件更新状态
                    emits('uploadData', res.data)
                }

                console.log(props.data.isFriend == res.data.isFriend)

            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('toast.getMsgFail'));
        });
};
onMounted(() => {
})
emitter.on('newAvatarId', (id) => {
    avatarId.value = id
    chatPaneLink.value = `uniwebview://openchatpanel?avatarId=${id}&roleId=${props.roleId}`
    // console.log(chatPaneLink.value)
})
</script>
<style lang="scss" scoped>
@import '@/assets/css/index/user.scss';

.dialogCon {
    width: 99vw;
    height: 90vh;
}
</style>