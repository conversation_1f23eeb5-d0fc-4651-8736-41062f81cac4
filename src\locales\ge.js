const ge = {
    index:{
        title:'Startseite',
        menuItems: {
            Dynamic: 'Beiträge',
            Favorites: '<PERSON><PERSON> Favorite<PERSON>',
            Liked: 'G<PERSON><PERSON>llt mir',
            Messages: '<PERSON><PERSON><PERSON><PERSON>',
            weChat:'Momente',
            myDynamic:'<PERSON>ne <PERSON>iträ<PERSON>',
            myFavorites:'<PERSON>ne <PERSON>',
            myLiked:'Meine Gefällt-mir-Angaben',
            Hot:'Tendances',
            Recommend:'Empfehlen',
            New:'Neueste',
            Vote:'Raten&Erhalten',
        },
        user:{
            praised:'Likes gewinnen',
            collect:'Zu Favoriten',
            chat:'Chat',
            addFriends:'Freunde hinzufügen',
            friendRequest:'Freundeantrag',
            verificationMessage:'Überprüfungsinformation',
            sendNow:'Sofort senden',
            verificationMessagePlaceholder:'Wie geht es Ihnen! Schön, Sie kennenzulernen',
        },
        msg:{
            likeCollectTab:'Gefällt mir und Favorit',
            commentAitTab:"Komme<PERSON><PERSON> und{'@'}",
            reply:'Antworten',
            likeCond:'Ich mochte deinen Beitrag',
            likeConc:'Kommentar gemocht',
            likeConr:'Antwort gemocht',
            collectCon:'Ich habe deinen Beitrag gespeichert',
            commentCon:'Kommentiert deinen Beitrag',
            replyCon:'Antwortete',
            aitCon:"{'@'}du"
        }
    },
    detail:{
      replyNum:'Insgesamt %num% Antworten',
      seeMore:'Mehr anzeigen',
      stop:'Zusammenklappen',
      originalText:'Originaltext',
      translation:'Übersetzen',
      commentNum:'Kommentare',
      send:'Senden',
      reply:'Antworten',
      replied:'Antwortete',
      likeListTitle:'Likes von %num% Freunden'
    },
    add:{
       title:'Beitrag',
       input:'Bitte geben Sie den Inhalt des Beitrags ein',
       topic:'Thema hinzufügen',
       whoSee:{
          title:'Wem kann ich es zeigen',
          all:'Jeder',
          friend:'Freunde können es sehen',
          oneself:'Nur für dich sichtbar',
       },
       publish:'Freigeben',
       audioStart:'Klicken, um zu sprechen',
       audioEnd:'Klicken, um zu beenden',
       searchPlaceholder:'Suche nach deinen Freunden',
       confirm:'Bestätigen',
       audioPermission:'Bitte öffne zuerst die Mikrofonberechtigung',
       imagePermission:'Bitte aktivieren Sie zuerst die Kameraberechtigungen',
       aitUser:"{'@'}Benutzer"
    },
    report:{
       next:'Nächster Schritt',
       confirmReport:'Bestätigen Sie den Bericht',
       placeholder:'Bitte Inhalt eingeben',
       detailTitle:'Detaillierte Beschreibung',
       imgTitle:'Bildbeweis',
       reportSuccess:'Bericht erfolgreich',
       reportFail:'Bericht fehlgeschlagen',
       reportSuccessInfo:'Nach der Einreichung wird die Plattform aktiv überprüfen und verarbeiten; danke für Ihre Bemühungen, die soziale Umgebung aufrechtzuerhalten!',
       reportPublish:'Rückgabedynamik',
       reportOneTitleAfterD:'Sie reportieren den Beitrag von %name%',
       reportOneTitleAfterC:'Sie melden einen Kommentar von %name%',
       reportOneTitleAfterR:'Sie melden eine Antwort von %name%',
       reportTwoTitle:'Du hast  %title% gewählt',
       reportEndTitleAfterD:'Ihr Bericht für den Beitrag von %name% gehört zu %title%',
       reportEndTitleAfterC:'Ihr Bericht für Kommentare von %name% gehört zu %title%',
       reportEndTitleAfterR:'Ihr Bericht für Antworten von %name% gehört zu %title%',
    },
    tooltip:{
        delete:'Löschen',
        modify:'Ändern',
        cancelCollect:'Nicht mehr favorisieren',
        report:'Bericht',
        block:'Sperren',
    },
    delete:{
      deleteCon:'Sind Sie sicher, dass Sie diesen Inhalt löschen möchten?',
      deleteCancel:'Abbrechen',
      deleteConfirm:'Bestätigen',
      blockCon:'Möchten Sie wirklich sperren?',
    },
    toast:{
        likeSuccess:'Gefällt mir erfolgreich',
        likeCancel:'Gefällt wurde abgesagt',
        likeFail:'Gefällt ist fehlgeschlagen',
        collectSuccess:'Favorit erfolgreich',
        collectCancel:'Nicht favorisiert',
        collectFail:'Lieblingsbeitrag fehlgeschlagen',
        publishSuccess:'Beitrag erfolgreich',
        publishFail:'Beitrag fehlgeschlagen',
        modifySuccess:'Die Modifikation war erfolgreich',
        topicInfo:'Sie können bis zu 5 Themen auswählen',
        aitInfo:"Bis zu {'@'} 5 Benutzer",
        ait:"Bitte geben Sie mindestens 1 Zeichen vor {'@'} ein",
        dynamicInput:'Laden Sie mindestens eines der folgenden Elemente hoch: dynamischer Inhalt, Bilder oder Videos',
        nextInfo:'Bitte wählen Sie zuerst eine Option aus',
        reportSuccess:'Bericht erfolgreich',
        reportFail:'Bericht fehlgeschlagen',
        audioTextSuccess:'Spracherkennung erfolgreich',
        audioTextFail:'Spracherkennung fehlgeschlagen',
        translationSuccess:'Übersetzung erfolgreich',
        translationFail:'Übersetzung fehlgeschlagen',
        uploadImageFail:'Hochladen fehlgeschlagen',
        deleteSuccess:'Löschen erfolgreich',
        deleteFail:'Löschung fehlgeschlagen',
        // 新加
        imageLimit:'Die Dateigröße darf %num% MB nicht überschreiten',
        imageNum:'Bis zu 9 Bilder können hochgeladen werden',
        uploadPrompt:'Klicken Sie zum Hochladen von Bildern&Videos',
        filePrompt:'(Dateiformate Doc, docx und pdf werden unterstützt, und die Dateigröße darf 5 mb nicht überschreiten)',
        imageBefore:'Nicht mehr als 4 Mb für ein einziges Bild',
        imageShowToast:'Upload-Datei ist zu groß',
        audioFail:'Fehler beim Beenden der Aufnahme',
        collectCancelFail:'Stornierung fehlgeschlagen',
        collectCancelSuccess:'Stornierung erfolgreich',
        dynamicFail:'Beitrag existiert nicht',
        addCommentViolation:'Der von Ihnen eingereichte Inhalt wird verdächtigt, gegen Vorschriften zu verstoßen. Bitte ändern Sie ihn und reichen Sie ihn erneut ein. Ändern und erneut einreichen.',
        addCommentFail:'Kommentar konnte nicht hinzugefügt werden',
        addReplyFail:'Antwort konnte nicht hinzugefügt werden',
        addDynamicViolation:'Der von Ihnen eingereichte "Post"-Inhalt wird verdächtigt, gegen Vorschriften zu verstoßen. Bitte ändern Sie ihn und reichen Sie ihn erneut ein.',
        addTopicViolation:'Der Inhalt des von Ihnen eingereichten "Themas" wird verdächtigt, gegen Vorschriften zu verstoßen. Bitte ändern Sie ihn und reichen Sie ihn erneut ein.',
        addImageViolation:'Der von Ihnen eingereichte "Bild"-Inhalt wird verdächtigt, gegen Vorschriften zu verstoßen. Bitte ändern Sie ihn und reichen Sie ihn erneut ein.',
        topicCon:'Der Themeninhalt darf nicht leer sein',
        getMsgFail:'Informationen konnten nicht abgerufen werden',
        loginFail:'Anmeldung fehlgeschlagen',
        aitInfoPermission:'Derzeit nur für Sie sichtbar',
        alreadyReport:'Sie haben mehrfach berichtet, bitte warten Sie auf das Feedback der Plattform',
        commentAfterDelete:'Kommentar gelöscht',
        replyAfterDelete:'Antwort gelöscht',
        msgDataListFail:'Fehler beim Abrufen der Daten',
        videoLimit:'La vidéo ne doit pas dépasser 25 Mo',
        videoPrompt:'Vous pouvez téléverser un maximum de 1 vidéo',
        videoToast:'Seules les images ou les vidéos peuvent être téléversées',
        imageTitle:'Téléverser une image',
        videoTitle:'Téléverser une vidéo',
        applySuccess:'Antrag erfolgreich gesendet',
        applyFail:'Antrag gesendet fehlgeschlagen',
        blacklistPrompt:'Freunde können nicht von der Blacklist hinzugefügt werden',
        friendNumPrompt:'Die Freundezahl des anderen ist voll',
        myNumPrompt:'Die aktuelle Freundezahl ist voll',
        failedPrompt:'Parameterfehler',
        alignPrompt:'Sie haben den anderen bereits als Freund hinzugefügt, Sie können den Antrag nicht erneut senden',
        applyMyPrompt:'Man kann sich selbst nicht hinzufügen',
        alignApply:'Du hast bereits eine Freundschaftsanfrage gesendet. Du kannst in 48 Stunden erneut eine Anfrage senden',
        blockSuccess:'Der Benutzer wurde der Schwarzen Liste hinzugefügt',
        blockFail:'Blockung fehlgeschlagen',
        blockListFull:'Die Blockliste ist voll',
        checkAgreementPrompt:'Du hast《Inhaltsveröffentlichungs-Erklärung》nicht zugestimmt und kannst keine Nachrichten veröffentlichen',
        AgreementFile:'Sie haben die Dokument gelesen und akzeptiert',
        fileTitle:'《Inhaltsveröffentlichungs-Erklärung》',
        sameLanguagePrompt:'Aktuell in derselben Sprache, keine Übersetzung erforderlich',

    },
    vote:{
        voteProgress:'Im Gange',
        voteEnd:'Beendet',
        voteSettle:'Abgerechnet',
        oneselfNum:'Abgestimmt',
        voteNum:'{num} Münze',
        timeName:'Verbleibende Zeit',
        allNum:'Gesamtanzahl der Münzen',
        participateInVoting:'Gesamtanzahl der Spieler:',
        getCoins:'Dieses Mal haben Sie {num} Münzen erhalten',
        voteBtn:'Wählen',
        voteTitle:'Abstimmen für {num}',
        inputInfo:'Bitte wählen Sie die Menge',
        voteConfirm:'Bestätigen',
        voteSuccess:'Erfolg',
        voteFail:'Misserfolg',
        statusEnd:'Das Event ist beendet',
        voteTnfo:'Die minimale Anzahl an Münzen, die für die Teilnahme am Event erforderlich ist, beträgt 1',
        hold:'Halten',
        balance:'Ihr aktueller Kontostand ist unzureichend. Bitte laden Sie auf',
        remainingTimeData:'{days} Tage {hours} Stunden {minutes} Minuten {seconds} Sekunden',
        questionInfo:'Alle Münzen der Benutzer gehen in den Preispool für dieses Event, und Benutzer, die richtig raten, teilen sich alle 【Zeit-Raum-Münzen】 im Preispool entsprechend der Anzahl der geratenen Münzen auf.',
    },
    video:{
        videoIndex:'Sag etwas...',
        videoDetail:'Detailseite',
        videoTitle:'Video',
    },
    empty:{
        comment:'Noch keine Kommentare',
        list:'Keine Daten verfügbar',
        content:'Kein Inhalt verfügbar',
        message:'Keine Nachrichten verfügbar'
    }
}

export default ge;

