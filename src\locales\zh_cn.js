const zh_cn = {
    index:{
        title:'动态主页',
        menuItems: {
            Dynamic: '动态',
            Favorites: '收藏',
            Liked: '赞过',
            Messages: '消息',
            weChat:'朋友圈',
            myDynamic:'我的动态',
            myFavorites:'我的收藏',
            myLiked:'我赞过的',
            Hot:'热门',
            Recommend:'推荐',
            New:'最新',
            Vote:'猜猜猜',
        },
        user:{
            praised:'获赞',
            collect:'收藏',
            chat:'好友对话',
            addFriends:'添加好友',
            friendRequest:'好友申请',
            verificationMessage:'验证信息',
            sendNow:'立即发送',
            verificationMessagePlaceholder:'你好呀！很高兴认识你',
        },
        msg:{
            likeCollectTab:'赞和收藏',
            commentAitTab:"评论和{'@'}",
            reply:'回复',
            likeCond:'点赞了你的动态',
            likeConc:'点赞了你的评论',
            likeConr:'点赞了你的回复',
            collectCon:'收藏了你的动态',
            commentCon:'评论了你的动态',
            replyCon:'回复了我',
            aitCon:"{'@'}了你"
        }
    },
    detail:{
      replyNum:'共%num%条回复',
      seeMore:'查看更多',
      stop:'收起',
      originalText:'原文',
      translation:'翻译',
      commentNum:'条评论',
      send:'发送',
      reply:'回复',
      replied:'回复了',
    //   have:'已有',
      likeListTitle:'来自%num%位好友的赞'
    },
    add:{
       title:'发布动态',
       input:'请输入动态内容',
       topic:'添加话题',
       whoSee:{
          title:'可以给谁看',
          all:'所有人',
          friend:'好友可见',
          oneself:'仅自己可见',
       },
       publish:'发布',
       audioStart:'点击说话',
       audioEnd:'点击结束',
       searchPlaceholder:'搜索你的朋友',
       confirm:'确认',
       audioPermission:'请先打开麦克风权限',
       imagePermission:'请先打开相机权限',
       aitUser:"{'@'}用户",
    },
    report:{
       next:'下一步',
       confirmReport:'确认举报',
       placeholder:'请输入内容',
       detailTitle:'详情描述',
       imgTitle:'图片证据',
       reportSuccess:'举报成功',
       reportFail:'举报失败',
       reportSuccessInfo:'提交后平台将积极核实并处理，感谢您对社交环境的维护！',
       reportPublish:'返回动态',
       reportOneTitleAfterD:'您正在举报%name%的动态',
       reportOneTitleAfterC:'您正在举报%name%的评论',
       reportOneTitleAfterR:'您正在举报%name%的回复',
       reportTwoTitle:'您选择了 %title%',
       reportEndTitleAfterD:'您举报%name%的动态选择了%title%',
       reportEndTitleAfterC:'您举报%name%的评论选择了%title%',
       reportEndTitleAfterR:'您举报%name%的回复选择了%title%',
    },
    tooltip:{
        delete:'删除',
        modify:'修改',
        cancelCollect:'取消收藏',
        report:'举报',
        block:'屏蔽用户'
    },
    delete:{
      deleteCon:'确认要删除该内容吗？',
      deleteCancel:'取消',
      deleteConfirm:'确定',
      blockCon:'确定要屏蔽吗？'
    },
    toast:{
        likeSuccess:'点赞成功',
        likeCancel:'已取消点赞',
        likeFail:'点赞失败',
        collectSuccess:'收藏成功',
        collectCancel:'已取消收藏',
        collectFail:'收藏失败',
        publishSuccess:'发布成功',
        publishFail:'发布失败',
        modifySuccess:'修改成功',
        topicInfo:'最多可选择五个话题哟',
        aitInfo:"最多可{'@'}5名用户",
        ait:"至少输入一个字符再{'@'}哟",
        dynamicInput:'动态内容、图片和视频至少上传一种',
        nextInfo:'请先选择一项',
        reportSuccess:'举报成功',
        reportFail:'举报失败',
        audioTextSuccess:'语音转文字成功',
        audioTextFail:'语音转文字失败',
        translationSuccess:'翻译成功',
        translationFail:'翻译失败',
        uploadImageFail:'上传失败',
        deleteSuccess:'删除成功',
        deleteFail:'删除失败',
        imageLimit:'文件大小不能超过 %num%MB',
        imageNum:'最多可上传9张图',
        uploadPrompt:'请点击上传图片&视频',

        videoLimit:'视频最大不超过25M',
        videoPrompt:'最多上传1个视频',
        videoToast:'只能上传图片或视频',
        imageTitle:'上传图片',
        videoTitle:'上传视频',
        applySuccess:'发送申请成功',
        applyFail:'发送申请失败',
        blacklistPrompt:'黑名单中不能添加好友',
        friendNumPrompt:'对方好友数量已满',
        myNumPrompt:'当前好友数量已满',
        failedPrompt:'参数错误',
        alignPrompt:'已添加对方为好友，不可再次申请',
        applyMyPrompt:'不能添加自己',

        filePrompt:'(支持doc、docx、pdf文件格式，文件大小不能超过5MB)',
        imageBefore:'单张图片不超过4MB',
        imageShowToast:'上传文件过大',
        audioFail:'结束录音出错',
        collectCancelFail:'取消失败',
        collectCancelSuccess:'取消成功',
        dynamicFail:'动态不存在',
        addCommentViolation:'您所提交的内容涉嫌违规，请修改后重新提交。',
        addCommentFail:'添加评论失败',
        addReplyFail:'添加回复失败',
        addDynamicViolation:'您所提交的“动态”内容涉嫌违规，请修改后重新提交。',
        addTopicViolation:'您所提交的“话题”内容涉嫌违规，请修改后重新提交。',
        addImageViolation:'您所提交的“图片”内容涉嫌违规，请修改后重新提交。',
        topicCon:'话题内容不能为空',
        getMsgFail:'获取信息失败',
        loginFail:'登录失败',
        aitInfoPermission:'当前仅自己可见',
        alreadyReport:'您已举报多次，请等待平台反馈',
        commentAfterDelete:'评论已删除',
        replyAfterDelete:'回复已删除',
        msgDataListFail:'数据获取失败',
        alignApply:'已申请好友，可四十八小时后再次申请',
        blockSuccess:'已将用户添加至黑名单',
        blockFail:'屏蔽失败',
        blockListFull:'屏蔽列表已满',
        checkAgreementPrompt:'您未同意《内容发布声明》，无法发布动态',
        AgreementFile:'您已阅读并同意该文件',
        fileTitle:'《内容发布声明》',
        sameLanguagePrompt:'当前处于同一语言，暂不翻译',

    },
    vote:{
        voteProgress:'进行中',
        voteEnd:'已结束',
        voteSettle:'已结算',
        oneselfNum:'已投',
        voteNum:'{num} 币', 
        timeName:'剩余时间',
        allNum:'总币数', 
        participateInVoting:'总参与人数：',
        getCoins:'本次您获得{num}币',
        voteBtn:'选择', 
        voteTitle:'投给{num}',
        inputInfo:'请选择数量', 
        voteConfirm:'确认',
        voteSuccess:'成功',
        voteFail:'失败',
        statusEnd:'活动已结束',
        voteTnfo:'参与活动至少投币数量为1',
        hold:'持有',
        balance:'当前账户余额不足，请及时充值',
        remainingTimeData:'{days}天{hours}时{minutes}分{seconds}秒' ,
        questionInfo:'用户的所有投币将全部计入本次活动的奖池中，猜对的用户按照所投币的数量来瓜分奖池内的所有时空币。'
    },
    video:{
        videoIndex:'说点什么...',
        videoDetail:'详情',
        videoTitle:'视频',
    },
    empty:{
        comment:'暂无评论',
        list:'暂无数据',
        content:'暂无内容',
        message:'暂无消息'
    }
}

export default zh_cn;

