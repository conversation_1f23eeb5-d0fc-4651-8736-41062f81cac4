<template>
    <div class="item">
        <div class="text-title">
            <span>{{ title }}</span>
        </div>
        <div class="text-content">
            <div class="text-reward">
                <div class="text-reward-item"
                    :class="{ 'first': item.id === 1, 'second': item.id === 2, 'third': item.id === 3 }"
                    v-for="item in data" :key="item.id">
                    <div class="ranking">{{ item.name }}</div>
                    <div class="reward">
                        <img :src="item.src" alt="">
                        <span>x {{ item.reward }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref } from 'vue';
import one from '@/assets/images/match/one.webp';
import two from '@/assets/images/match/two.webp';
import third from '@/assets/images/match/third.webp';
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    firstPlaceReward: {
        type: Number,
        default: 0
    },
    secondPlaceReward: {
        type: Number,
        default: 0
    },
    thirdPlaceReward: {
        type: Number,
        default: 0
    }
})
const data = ref([{ id: 1, name: 'NO.1', reward: props.firstPlaceReward, src: one }, { id: 2, name: 'NO.2', reward: props.secondPlaceReward, src: two }, { id: 3, name: 'NO.3', reward: props.thirdPlaceReward, src: third }])

</script>
<style lang="scss" scoped>
.item {
    width: 100%;
    height: 100%;

    .text-title {
        text-align: center;
        font-size: 42px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 20px;
        width: 100%;
    }

    .text-content {
        background: #FFFFFF;
        border-radius: 4px;
        padding: 42px 62px;

        .text-reward {
            background: #FFFFFF;
            border-radius: 4px;
            padding: 72px 162px 80px;
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 30px;
            align-items: center;
            justify-content: center;

            .text-reward-item {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .ranking {
                    font-weight: 500;
                    font-size: 30px;
                    color: #333333;
                    margin-bottom: 8px;
                }

                .reward {
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    width: 320px;
                    height: 248px;
                    // background: #DFF4FE;
                    border-radius: 27px 0px 27px 0px;
                    font-size: 30px;
                    color: #5D5D5D;

                    img {
                        width: 164px;
                        height: 104px;
                    }
                }

            }

            .first {
                .ranking {
                    color: #E1A61B;
                }

                .reward {
                    background: linear-gradient(180deg, #FFFBE6 0%, #FFFFFF 100%);
                    border: 2px solid #FFF1CA;
                }

            }

            .second {
                .ranking {
                    color: #707D95;
                }

                .reward {
                    background: linear-gradient(180deg, #F3F6FE 0%, #FFFFFF 100%);
                    border: 2px solid #EBF1FF;
                }
            }

            .third {
                .ranking {
                    color: #CC7F6C;
                }

                .reward {
                    background: linear-gradient(180deg, #FFF3E6 0%, #FFFFFF 100%);
                    border: 2px solid #FFF1E3;
                }
            }
        }
    }
}
</style>
