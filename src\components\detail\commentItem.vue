<template>
    <div class="item" @click.stop="clickReply">
        <div class="left">
            <avatar :url="data.avatart" :userId="data.userId" className="comment"></avatar>
        </div>
        <div class="right">
            <div class="info">
                <div class="name">{{ data.userName }}</div>
                <div class="msg"> {{ translationCon }}</div>
                <img v-if="data.images" :src="data.images" @click.stop="handleClick(data.images)" alt="">
            </div>
            <div class="btn">
                <div class="time">{{ time(data.createTime) }}
                    <translation v-if="data.content.trim() && !isOnlyEmoji(data.content)" :content="data.content"
                        :language="data.language" @changeTranslationCon="changeTranslationCon"></translation>
                </div>
                <div class="bottomItem">
                    <img :src="imgLoveUrl" @click.stop="changeLike(data.id)" />
                    <div>{{ data.likeCount }}</div>
                </div>
                <popover :data="data" tooltipType="commentReport"></popover>
            </div>
            <!-- 判断是否有回复 -->

            <div class="reply" v-if="data.countReply > 0 && !showReply" @click.stop="changeReply">
                <span>{{ getReplyTitle(data.countReply) }}</span>
                <!-- <span>{{ $t('detail.all') }} {{ data.countReply }} {{ $t('detail.replyNum') }}</span> -->
            </div>
            <div v-if="showReply">
                <div v-for="item in replyData" :key="item.id">
                    <Reply :data="item" :commentId="data.id" @getReplyFocus="getReplyFocus"
                        @deleteRelpyList="deleteRelpyList">
                    </Reply>
                </div>
                <div v-if="replyData.length < data.countReply" class="spreadMore" @click="spreadMore">
                    <span>{{ $t('detail.seeMore') }}</span>
                    <img src="@/assets/images/detail/spread.png" />
                </div>
                <!-- 收起 -->
                <div class="stop" @click.stop="stop">{{ $t('detail.stop') }}</div>
            </div>
        </div>
    </div>

</template>
<script setup>
import { ymdDianTime, isOnlyEmoji } from "@/utils/time.js"
import { ref, reactive, onMounted, computed, onUnmounted, watch, defineAsyncComponent } from 'vue';
import { showToast } from 'vant';
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
const Reply = defineAsyncComponent(() =>
    import('@/components/detail/reply.vue')
);
const translation = defineAsyncComponent(() =>
    import('@/components/common/translation.vue')
);
import { isSeleteDetailLikeComment } from "@/assets/js/select.js"
import { getReply, like, collect } from "@/api/home.js"
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    }
});
const imgLoveUrl = ref()
const pageNum = ref(1)
const pageSize = ref(20)
const replyData = ref([])
const replyTotal = ref(0)
const translationCon = ref('')
const handleClick = ((data) => {
    showImagePreview({
        images: [data],
    });
})
// 计算时间
const time = ((data) => {
    return ymdDianTime(data)
})
// 查看更多列表
const spreadMore = () => {
    // 更新回复数据
    pageNum.value++
    getReplyList()
}
const showReply = ref(false)
// 向父组件传递popup显示事件
const changeReply = (() => {
    showReply.value = !showReply.value
    //查询回复列表
    pageNum.value = 1
    getReplyList()
})
// 收起
const stop = (() => {
    showReply.value = !showReply.value
})
//查询回复列表
const getReplyList = () => {
    const query = {
        commentId: props.data.id,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        limitId: replyData.value.length ? replyData.value[0].id : 0
    }
    getReply(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                props.data.countReply = res.total
                if (res.rows) {
                    if (pageNum.value == 1) {
                        replyData.value = res.rows
                    } else {
                        res.rows.forEach((item) => {
                            replyData.value.push(item)
                        })

                    }
                } else {
                    replyData.value = []
                }

            }
        })
        .catch(function (error) {
            console.log(error);
        });
}
const getReplyTitle = ((data) => {
    const str = t('detail.replyNum');
    const newStr = str.replace('%num%', data);
    return newStr
})
onMounted(() => {
    translationCon.value = props.data.content
    props.data.isLike ? imgLoveUrl.value = isSeleteDetailLikeComment.selete : imgLoveUrl.value = isSeleteDetailLikeComment.noselete
})
//获取到翻译组件传得值
const changeTranslationCon = (data) => {
    translationCon.value = data
}
// 点击点赞
const changeLike = (id) => {
    const query = {
        dyNamicsId: id,
        likeType: '1'
    }
    like(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                console.log(props.data)
                if (props.data.isLike) {
                    props.data.likeCount--
                    props.data.isLike = 0
                    showToast(t('toast.likeCancel'));
                    imgLoveUrl.value = isSeleteDetailLikeComment.noselete
                } else {
                    props.data.likeCount++
                    props.data.isLike = 1
                    showToast(t('toast.likeSuccess'));
                    imgLoveUrl.value = isSeleteDetailLikeComment.selete
                }
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('toast.likeFail'))
        });
};
// 定义组件的事件
const emits = defineEmits(['getFocus'])
// 点击回复
const clickReply = () => {
    console.log('点击获取焦点')
    emits('getFocus', props.data.id, props.data.userId, props.data.userName, '1')
}
// 
const getReplyFocus = (id, userId, userName) => {
    console.log(userId, userName)
    // clickReply()
    emits('getFocus', id, userId, userName, '2')
}
// 监听回复成功
emitter.on('addReplySuccess', (data) => {
    console.log('回复成功')
    // 判断当前是否是comment id 调用getReplyList
    if (data == props.data.id) {
        pageNum.value = 1
        getReplyList()
    }
})
// 监听回复删除成功
const deleteRelpyList = ((commentId) => {
    console.log(commentId)
    props.data.countReply--
    // pageNum.value = 1
    // getReplyList()
    replyData.value = replyData.value.filter(item => item.id !== commentId);
})
onUnmounted(() => {
    emitter.off('addReplySuccess')
})
</script>
<style lang="scss" scoped>
@import '@/assets/css/detail/commentItem.scss';

.spreadMore {
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--bearTtileColor);
    font-size: var(--size_20);

    img {
        margin-left: 2px;
        width: 24px;
        height: 24px;
    }
}

.stop {
    margin: 10px 0;
    font-size: 20px;
    color: #1E3E64;
}
</style>