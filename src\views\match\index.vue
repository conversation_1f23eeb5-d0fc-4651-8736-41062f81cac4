<template>
    <div class="match-container">
        <!-- 顶部导航栏 -->
        <div class="nav-tabs">
            <div class="leftNav">
                <img @click="onClickLeft" src="@/assets/images/match/back.webp" alt="">
                <span>赛事</span>
            </div>
            <div class="centerNav">
                <Tab :data="tabs" type="msg" @changeTab="changeTab"></Tab>
            </div>
        </div>
        <!-- 内容区域 -->
        <div class="content-container" ref="scrollContainer" :style="scrollBehaviorStyle">
            <!-- 视频卡片网格 -->
            <van-list 
                class="vanList" 
                ref="waterfallList" 
                v-model:loading="loading" 
                loading-text="加载中"
                :finished="finished" 
                @load="changeWaterfallData" 
                :offset="300" 
                :immediate-check="true"
            >
                <div v-if="videoList.length" class="masonry-wrapper">
                    <div v-masonry  transition-duration="0" item-selector=".item">
                        <div v-masonry-tile class="item" v-for="(item, index) in videoList" :key="index"
                            @click="videoCardClick(item)">
                            <img class="coverImage"
                                src="https://fracdn.hapmetasocialltd.com/gamett/2025/05/29/0c614a4e-89aa-4737-b043-eda00f56c408.png"
                                alt="">
                            <div class="card-info">
                                <div class="info-title">第四届"春天你好"短视频大赛</div>
                                <div class="info-con">
                                    <div class="info-date">赛事时间: {{ item.dateRange }}</div>
                                    <div class="info-icon"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </van-list>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, watch, computed, defineAsyncComponent, onActivated } from 'vue';
const Tab = defineAsyncComponent(() =>
    import('@/components/index/tab.vue')
);
import { useRouter, useRoute } from 'vue-router';
import _ from 'lodash';
import { createMasonryManager, ScrollPositionManager } from '@/utils/masonryManager';

const router = useRouter();
const route = useRoute();
import emitter from '@/utils/mitt';
const tabs = ref([{ id: 1, title: '视频' }, { id: 2, title: '摄影' }, { id: 3, title: '时政' }, { id: 4, title: '爱情' }, { id: 5, title: '友谊' }]);

// van-list相关状态
const loading = ref(false);
const finished = ref(false);
const scrollContainer = ref(null);
const waterfallList = ref(null);

// 控制内容区域显示状态
const isContentVisible = ref(true);

// 控制滚动行为
const isInstantScroll = ref(false);
const scrollBehaviorStyle = computed(() => {
    return isInstantScroll.value
        ? { scrollBehavior: 'auto' }
        : { scrollBehavior: 'smooth' };
});

// 创建独立的瀑布流管理器和滚动位置管理器
const masonryManager = createMasonryManager('match-page');
const scrollPositionManager = ref(null);

// 初始化滚动位置管理器
const initScrollManager = () => {
    if (scrollContainer.value) {
        scrollPositionManager.value = new ScrollPositionManager(
            scrollContainer.value,
            'matchScrollPosition'
        );
    }
};

// 重新布局函数 - 使用独立的瀑布流管理器
const relayoutMasonry = (delay = 100) => {
    if (scrollContainer.value) {
        // 确保瀑布流管理器已初始化
        masonryManager.init(scrollContainer.value);
        // 触发重绘
        masonryManager.layout(delay);
    }
};

// 切换tab
const changeTab = (index) => {
    console.log(index);
    // Tab切换后也需要重新布局
    nextTick(() => {
        relayoutMasonry();
    });
}

// 返回上一页
const onClickLeft = () => {
    console.log('返回按钮点击');

    // 发送事件通知index页面准备接收返回
    emitter.emit('backFromMatch');

    router.back();
};

// 瀑布流加载更多数据
const changeWaterfallData = _.debounce(() => {
    console.log('加载更多数据');
    // 设置loading状态
    loading.value = true;

    // 模拟加载更多数据
    setTimeout(() => {
        // 添加新数据
        videoList.value.push(
            {
                id: videoList.value.length + 1,
                title: '第四届"春天你好"短视频大赛',
                subTitle: '暨第四届"春天你好"短视频大赛',
                tagName: '清明户外课堂',
                dateText: '2024年4月5日全天',
                hashtags: ['#去踏青', '#四月踏青#'],
                dateRange: '2025/05/28-2025/06/03'
            }
        );

        // 通知v-masonry重新布局
        nextTick(() => {
            relayoutMasonry();
        });

        // 结束loading状态
        loading.value = false;

        // 如果数据达到一定数量，标记为加载完成
        if (videoList.value.length >= 20) {
            finished.value = true;
        }
    }, 1000);
}, 200);

// 视频卡片点击
const videoCardClick = (item) => {
    console.log(item);

    // 使用滚动位置管理器保存位置
    if (scrollPositionManager.value) {
        scrollPositionManager.value.savePosition();
    }

    router.push({
        path: '/matchDetail',
        query: {
            id: '1'
        }
    })
    // 通知打开竖屏
    window.location.href = "uniwebview://changeToPortrait";
}

// 优化后的无感恢复滚动位置函数
const restoreScrollPositionOptimized = () => {
    console.log('开始match页面无感恢复滚动位置');

    if (!scrollPositionManager.value) {
        console.log('滚动位置管理器未初始化');
        return;
    }

    // 启用即时滚动模式（禁用平滑滚动）
    isInstantScroll.value = true;

    // 使用滚动位置管理器进行无感恢复
    scrollPositionManager.value.restorePositionSeamlessly(masonryManager, () => {
        // 恢复普通滚动模式
        setTimeout(() => {
            isInstantScroll.value = false;
            console.log('match页面滚动位置恢复完成');
        }, 100);
    });
};

// 监听从详情页返回的事件
const handleFromDetail = () => {
    console.log('从详情页返回');
    
    // 使用优化后的无感恢复滚动位置函数
    restoreScrollPositionOptimized();
};

// 监听路由变化
watch(() => route.path, (newPath, oldPath) => {
    if (newPath === '/match' && oldPath && oldPath.includes('/matchDetail')) {
        handleFromDetail();
    }
}, { immediate: true });

// 监听openMatch事件
emitter.on('openMatch', () => {
    // 使用滚动位置管理器保存位置
    if (scrollPositionManager.value) {
        scrollPositionManager.value.savePosition();
    }

    router.push({
        path: '/matchDetail',
        query: {
            id: '1'
        }
    })
});

// 在组件挂载后初始化
onMounted(() => {
    // 初始化滚动位置管理器
    nextTick(() => {
        initScrollManager();
        // 初始化瀑布流布局
        relayoutMasonry();
    });

    // 添加窗口大小变化监听
    window.addEventListener('orientationchange', relayoutMasonry);
});

onActivated(() => {
    console.log('match页面激活');

    // 确保管理器已初始化
    if (!scrollPositionManager.value) {
        initScrollManager();
    }

    // 先恢复滚动位置
    nextTick(() => {
        restoreScrollPositionOptimized();
    });
})

// 组件卸载时清理
onUnmounted(() => {
    // 移除事件监听
    emitter.off('openMatch');
    window.removeEventListener('orientationchange', relayoutMasonry);
});

// 视频列表数据
const videoList = ref([
    {
        id: 1,
        title: '第四届"春天你好"短视频大赛',
        subTitle: '暨第四届"春天你好"短视频大赛',
        tagName: '清明户外课堂',
        dateText: '2024年4月5日全天',
        hashtags: ['#去踏青', '#四月踏青#'],
        dateRange: '2025/05/28-2025/06/03'
    },
    {
        id: 2,
        title: '第四届"春天你好"短视频大赛',
        subTitle: '暨第四届"春天你好"短视频大赛',
        tagName: '清明户外课堂',
        dateText: '2024年4月5日全天',
        hashtags: ['#去踏青', '#四月踏青#'],
        dateRange: '2025/05/28-2025/06/03'
    },
    {
        id: 3,
        title: '第四届"春天你好"短视频大赛',
        subTitle: '暨第四届"春天你好"短视频大赛',
        tagName: '清明户外课堂',
        dateText: '2024年4月5日全天',
        hashtags: ['#去踏青', '#四月踏青#'],
        dateRange: '2025/05/28-2025/06/03'
    },
    {
        id: 4,
        title: '第四届"春天你好"短视频大赛',
        subTitle: '暨第四届"春天你好"短视频大赛',
        tagName: '清明户外课堂',
        dateText: '2024年4月5日全天',
        hashtags: ['#去踏青', '#四月踏青#'],
        dateRange: '2025/05/28-2025/06/03'
    },
    {
        id: 5,
        title: '第四届"春天你好"短视频大赛',
        subTitle: '暨第四届"春天你好"短视频大赛',
        tagName: '清明户外课堂',
        dateText: '2024年4月5日全天',
        hashtags: ['#去踏青', '#四月踏青#'],
        dateRange: '2025/05/28-2025/06/03'
    }
]);
</script>

<style lang="scss" scoped>
.match-container {
    background: linear-gradient(180deg, #EEEFF2 0%, #E7F1FF 100%);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    overflow: hidden; // 防止整体溢出
}


// 导航标签样式
.nav-tabs {
    display: flex;
    align-items: center;
    width: 100%;
    padding-bottom: 22px;
    flex-shrink: 0; // 防止导航栏被压缩
    position: relative;
    z-index: 10;

    .leftNav {
        display: flex;
        align-items: center;
        margin: 30px 109px 0 30px;

        img {
            width: 40px;
            height: 40px;
        }

        span {
            margin-left: 8px;
            font-size: 26px;
            color: rgba(51, 51, 51, 0.8);
        }
    }
}

// 内容区域样式
.content-container {
    padding: 0 78px;
    overflow: auto;
    -webkit-overflow-scrolling: touch; // 增加iOS滚动支持
    height: calc(100vh - 100px); // 设置一个固定高度，减去导航栏高度
    position: relative;
    will-change: scroll-position; // 提示浏览器优化滚动性能
    transition: opacity 0.3s ease; // 添加透明度过渡效果
}

// 添加隐藏状态类
.content-hidden {
    opacity: 0;
    pointer-events: none; // 防止用户在隐藏状态下交互
}

// 瀑布流容器
.vanList {
    width: 100%;
    padding-bottom: 40px; // 底部添加一些padding，防止内容被遮挡
}

.masonry-wrapper {
    width: 100%;
    padding-top: 10px; // 顶部添加一些padding
    min-height: 100%; // 确保最小高度为100%
}

// 确保v-masonry容器正确显示
[v-masonry] {
    width: 100% !important;
    margin: 0 auto;
    transform: translateZ(0); // 强制启用硬件加速
    backface-visibility: hidden; // 防止渲染闪烁
    perspective: 1000; // 改善3D性能
    will-change: transform; // 提示浏览器优化变换
    contain: layout style paint; // 优化渲染性能
}

// 视频卡片样式
.item {
    width: calc(50% - 8px) !important; // 强制设置宽度为50%减去间距的一半
    position: relative;
    min-height: 80px;
    box-sizing: border-box;
    margin: 0 0 16px 0 !important; // 只设置底部margin
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden; // 确保内容不溢出

    .coverImage {
        width: 100%;
        height: 347px;
    }
}

// 使用CSS选择器控制间距
.item:nth-child(odd) {
    margin-right: 8px !important; // 奇数项右边距8px
}

.item:nth-child(even) {
    margin-left: 8px !important; // 偶数项左边距8px
}

// 卡片底部信息
.card-info {
    padding: 15px 0;

    .info-title {
        padding-left: 24px;
        font-size: 28px;
        color: rgba(51, 51, 51, 0.9);
        margin-bottom: 4px;
    }

    .info-con {
        display: flex;
        padding-left: 24px;
        .info-date {
            flex: 1;
            font-size: 26px;
            color: rgba(51, 51, 51, 0.68);
        }
        .info-icon {
            width: 120px;
            height: 44px;
            background-image: url('@/assets/images/match/end.webp');
            background-size: 100% 100%;
        }
    }
}
</style>