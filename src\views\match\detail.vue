<template>
    <div class="match-detail-container">
        <!-- 顶部导航栏 -->
        <van-nav-bar class="custom-nav" :border="false">
            <template #left>
                <img class="back-icon" @click="onClickLeft" src="@/assets/images/match/back.webp" alt="">
            </template>
            <template #title>
                <span>第四届"春天你好"短视频大赛</span>
            </template>
        </van-nav-bar>
        <div class="match-detail-container-content">
            <!-- 横幅图片 -->
            <div class="banner-container">
                <!-- fit="contain" -->
                <van-image :src="bannerImageUrl" class="banner-image" fit="contain" />
                <div class="affix" @click="addClick">
                    <img src="@/assets/images/index/add.webp" />
                </div>
            </div>
            <!-- 标签导航栏 -->
            <div class="tabs-container">
                <van-tabs v-model:active="activeTab" sticky swipeable animated line-height="0" :border="false" @click-tab="onClickTab">
                    <van-tab v-for="(tab, index) in tabs" :key="index" :title="tab.title">
                        <div class="content-container">
                            <div class="content-text" v-if=" index === 0">
                                <Rule title="比赛内容" :data="matchContent" />
                            </div>
                            <div class="content-text" v-else-if="index === 1">
                                <Rule title="比赛规则" :data="matchRules" />
                            </div>
                            <div class="content-text" v-else-if="index === 2">
                                <Reward title="比赛奖励" />
                            </div>
                            <div class="content-text" v-else-if="index === 3">
                                {{ matchGuidelines }}
                            </div>
                            <div class="works-container" v-else-if="index === 4">
                                <!-- <div class="no-works">
                                    暂无参赛作品
                                </div> -->
                                <WaterfallList :activeTab="5" :isMatchType="true"></WaterfallList>
                            </div>
                        </div>
                    </van-tab>
                </van-tabs>
            </div>
        </div>
    </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, defineAsyncComponent, onActivated, onDeactivated, nextTick } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { showToast } from 'vant';
import emitter from '@/utils/mitt';

// 显式定义组件名称，以便与keep-alive的include列表匹配
defineOptions({
  name: 'matchDetail'
});

const Rule = defineAsyncComponent(() =>
    import('@/components/match/rule.vue')
);
const Reward = defineAsyncComponent(() =>
    import('@/components/match/reward.vue')
);
const WaterfallList = defineAsyncComponent(() =>
    import('@/components/index/waterfallList.vue')
);
const router = useRouter();
const route = useRoute();

// 获取路由参数
const matchId = ref(route.query.id || 1);

// 标签页状态
const activeTab = ref(0);
const tabs = ref([
    { title: '比赛内容', id: 1 },
    { title: '比赛规则', id: 2 },
    { title: '比赛奖励', id: 3 },
    { title: '参赛须知', id: 4 },
    { title: '参赛作品', id: 5 }
]);

// 横幅图片URL（使用蓝色背景的图片）
const bannerImageUrl = ref('https://fracdn.hapmetasocialltd.com/gamett/2025/05/29/0c614a4e-89aa-4737-b043-eda00f56c408.png');

// 比赛内容（静态数据）
const matchContent = ref(`2024 年国际短视频大赛
         大赛主题："盛世华诞 视听筑梦"，献礼新中国成立 75 周年，通过短视频展现中国文化、审美、气度和风采，助国际受众了解中国。竞赛单元机遇中国：展示新时代中国在市场、投资、绿色发展 字、对外合作等方面机遇。
文明互鉴：讲述东西方文明相互借鉴成果及中外友好交流故事。
自然人文：聚焦我国自然风光、美食美景、新城市、新地理、新人文，设泰安专题特邀创作单元。
Z 视角：围绕绿色议题和公共事务议题，鼓励中外 "Z 世代" 参与创作，阐释人类命运共同体理念。
`);

// 比赛规则（静态数据）
const matchRules = ref('1. 参赛作品必须原创，不得抄袭\n2. 作品主题必须符合比赛要求\n3. 每位参赛者最多可提交3件作品\n4. 提交作品格式为JPG或PNG，分辨率不低于300dpi\n5. 参赛作品不得含有违法内容\n6. 主办方拥有最终解释权');

// 比赛奖励（静态数据）
const matchRewards = ref('一等奖（1名）：奖金10000元\n二等奖（2名）：奖金5000元\n三等奖（3名）：奖金2000元\n优秀奖（10名）：奖金500元');

// 参赛须知（静态数据）
const matchGuidelines = ref('1. 参赛者需在规定时间内提交作品\n2. 参赛者需保证作品的原创性\n3. 获奖者需提供有效身份证明以领取奖金\n4. 参赛作品版权归主办方所有\n5. 参赛者需遵守比赛的所有规则');

// 缓存页面状态
const pageState = ref({
  activeTab: 0,
  scrollPosition: 0
});

// 添加缺失的addClick方法
const addClick = () => {
  console.log('添加按钮点击');
  showToast('参赛功能即将开放');
};

// 清理资源，释放内存
const cleanupResources = () => {
  console.log('清理matchDetail页面资源');
  // 释放可能占用内存的大型数据
  bannerImageUrl.value = '';
  matchContent.value = '';
  matchRules.value = '';
  matchRewards.value = '';
  matchGuidelines.value = '';
  
  // 重置状态
  pageState.value = { activeTab: 0, scrollPosition: 0 };
  
  // 如果有其他需要清理的资源，例如监听器、定时器等，可以在这里清理
};

// 返回上一页，避免使用window.location导致整页刷新
const onClickLeft = () => {
    console.log('返回按钮点击');
    
    // 判断是否返回到match页面，如果是则进行资源清理
    // 由于无法直接获取上一页的路径，我们将发送一个事件通知Layout组件更新缓存
    console.log('即将返回，通知移除matchDetail缓存');
    emitter.emit('removeMatchDetailCache');
    
    // 保存当前状态以便可能的下次恢复
    savePageState();
    
    // 先返回上一页
    router.back();
    
    // 使用延时执行，避免路由导航与原生通信冲突
    setTimeout(() => {
        // 通知打开横屏
        window.location.href = "uniwebview://changeToLandscape";
    }, 100);
};

// 保存页面状态
const savePageState = () => {
  const contentContainer = document.querySelector('.content-container');
  if (contentContainer) {
    pageState.value = {
      activeTab: activeTab.value,
      scrollPosition: contentContainer.scrollTop
    };
    console.log('保存页面状态:', pageState.value);
  }
};

// 恢复页面状态
const restorePageState = () => {
  console.log('恢复页面状态:', pageState.value);
  activeTab.value = pageState.value.activeTab;
  
  // 使用nextTick确保DOM已更新后再设置滚动位置
  nextTick(() => {
    const contentContainer = document.querySelector('.content-container');
    if (contentContainer) {
      contentContainer.scrollTop = pageState.value.scrollPosition;
      console.log('已恢复滚动位置:', pageState.value.scrollPosition);
    }
  });
};

emitter.on('backMatch', () => {
    router.back();
})

// 参赛按钮点击
const onParticipate = () => {
    showToast('参赛功能即将开放');
};

// 切换标签页
const onClickTab = (index) => {
    console.log(activeTab.value)
    console.log('点击标签页:', index);
    // 保存当前选中的标签页
    pageState.value.activeTab = activeTab.value;
};

// 页面加载时获取比赛详情
onMounted(() => {
    console.log('比赛ID:', matchId.value);
    console.log('matchDetail组件已挂载');
    // 这里可以添加获取比赛详情的API调用
});

// 当组件从缓存中被激活时调用
onActivated(() => {
    console.log('matchDetail页面激活，组件name:', 'matchDetail');
    // 重新初始化资源
    if (bannerImageUrl.value === '') {
      console.log('重新初始化资源');
      bannerImageUrl.value = 'https://fracdn.hapmetasocialltd.com/gamett/2025/05/29/0c614a4e-89aa-4737-b043-eda00f56c408.png';
      matchContent.value = `2024 年国际短视频大赛
         大赛主题："盛世华诞 视听筑梦"，献礼新中国成立 75 周年，通过短视频展现中国文化、审美、气度和风采，助国际受众了解中国。竞赛单元机遇中国：展示新时代中国在市场、投资、绿色发展 字、对外合作等方面机遇。
文明互鉴：讲述东西方文明相互借鉴成果及中外友好交流故事。
自然人文：聚焦我国自然风光、美食美景、新城市、新地理、新人文，设泰安专题特邀创作单元。
Z 视角：围绕绿色议题和公共事务议题，鼓励中外 "Z 世代" 参与创作，阐释人类命运共同体理念。`;
      matchRules.value = '1. 参赛作品必须原创，不得抄袭\n2. 作品主题必须符合比赛要求\n3. 每位参赛者最多可提交3件作品\n4. 提交作品格式为JPG或PNG，分辨率不低于300dpi\n5. 参赛作品不得含有违法内容\n6. 主办方拥有最终解释权';
      matchRewards.value = '一等奖（1名）：奖金10000元\n二等奖（2名）：奖金5000元\n三等奖（3名）：奖金2000元\n优秀奖（10名）：奖金500元';
      matchGuidelines.value = '1. 参赛者需在规定时间内提交作品\n2. 参赛者需保证作品的原创性\n3. 获奖者需提供有效身份证明以领取奖金\n4. 参赛作品版权归主办方所有\n5. 参赛者需遵守比赛的所有规则';
    }
    // 从缓存恢复时，恢复保存的状态
    restorePageState();
});

// 当组件被缓存时调用
onDeactivated(() => {
    console.log('matchDetail页面失活，保存状态');
    // 保存当前页面状态，以便下次激活时恢复
    savePageState();
});

onUnmounted(() => {
    console.log('matchDetail组件卸载');
    // 确保清理所有资源
    cleanupResources();
    // 移除事件监听
    emitter.off('backMatch');
});
</script>

<style lang="scss" scoped>
.match-detail-container {
    // background-color: #f5f7fa;
    // min-height: 100vh;
    height: 100vh;
    display: flex;
    flex-direction: column;

    // 自定义导航栏样式
    .custom-nav {
        background-color: #ffffff;

        .back-icon {
            width: 40px;
            height: 40px;
        }

        :deep(.van-nav-bar__content) {
            height: 100px;
        }

        :deep(.van-nav-bar__left) {
            font-size: 28px;
        }

        :deep(.van-nav-bar__title) {
            font-size: 28px;
            font-weight: 500;
            color: rgba(51, 51, 51, 0.9);
        }

        :deep(.van-nav-bar__title) {
            display: flex;
            align-items: center;
            height: 100%;
        }
    }

    .match-detail-container-content {
        flex: 1;
        overflow: hidden;
        padding: 0 78px;
        display: flex;
        flex-direction: column;

        // 横幅图片样式
        .banner-container {
            width: 100%;
            height: 300px;
            // height: 40%;
            // overflow: hidden;
            position: relative;

            .banner-image {
                width: 100%;
                height: 100%;
            }

            .affix {
                position: absolute;
                right: 0;
                bottom: 0;

                img {
                    width: 108px;
                    height: 108px;
                }
            }
        }

        // 标签导航栏样式
        .tabs-container {
            background: #DAE5F7;
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;

            :deep(.van-tabs__wrap) {
                border-bottom: 1px solid #f0f0f0;
                height: 88px;
                flex-shrink: 0;
            }

            :deep(.van-tabs) {
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            :deep(.van-tab) {
                font-size: 28px;
                color: #666666;
                background: #DAE5F7;
                padding: 0;
            }

            :deep(.van-tab__text--ellipsis) {
                overflow: inherit;
            }

            :deep(.van-tab--active) {
                font-weight: 500;
                font-size: 28px;
                color: #FFFFFF;
                background: #4DA6FF;
            }

            // :deep(.van-tabs__line) {
            //     background-color: #2970ff;
            // }

            :deep(.van-tabs__content) {
                flex: 1;
                overflow: hidden;
                position: relative;
            }


            :deep(.van-tab__panel) {
                height: 100%;
                overflow: hidden;
            }

            :deep(.van-tabs__wrap) {
                margin: 0 92px;
            }
        }

        :deep(.van-tabs__content--animated) {
            height: 100%;
        }

        // 内容区域样式
        .content-container {
            height: 100%;
            background: #F1F5FC;
            overflow-y: auto;
            -webkit-overflow-scrolling: touch;
            position: relative;

            &::after {
                content: '';
                display: block;
                height: 1px;
                opacity: 0.01;
                margin-bottom: -1px;
            }

            .content-text {
                margin: 60px 94px;
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
                align-items: center;
                font-size: 26px;
                line-height: 40px;
                color: #474747;
                white-space: pre-wrap;

                &:nth-child(1),
                &:nth-child(2),
                &:nth-child(3),
                &:nth-child(4) {
                    height: auto;
                }
            }

            .works-container {
                height: 100%;
                // display: flex;
                // justify-content: center;
                // align-items: center;
                // min-height: 200px;

                .no-works {
                    font-size: 16px;
                    color: #999;
                }
            }
        }
    }
}






// 悬浮按钮样式
.float-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 99;

    :deep(.van-button) {
        background-color: #2970ff;
        border-color: #2970ff;
        font-size: 14px;
        padding: 0 20px;
        height: 40px;
        box-shadow: 0 2px 8px rgba(41, 112, 255, 0.3);
    }
}
</style>
