<template>
    <div>
        <van-popup v-model:show="dialogVisible" title="" show-cancel-button @close="handleClose"
            @click.stop="onClickPopup" @click-overlay.stop="onClickOverlay" :close-on-click-overlay="false">
            <div class="deleteCon">
                <div class="title">{{ data.title }}</div>
                <div class="voteCon">
                    <div class="info">{{ $t('vote.inputInfo') }}</div>
                    <div class="input">
                        <img @click="reduceNumber" src="@/assets/images/vote/reduceNumber.webp" />
                        <van-field v-model="input" type="digit" @update:model-value="changeInput" @blur="handleInput"
                            placeholder="" :min="0" :max="coinNum" />
                        <img @click="addNumber" src="@/assets/images/vote/addNumber.webp" />
                    </div>
                </div>
                <div class="gold">
                    <div>{{ $t('vote.hold') }}:</div>
                    <img src="@/assets/images/vote/glod.webp" />
                    <div class="num">{{ coinNum }}</div>
                </div>
                <div class="bottom">
                    <div class="publish" @click="publish">{{ $t('vote.voteConfirm') }}</div>
                </div>

                <div class="close" @click="close">
                    <img src="@/assets/images/index/close.png" />
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import { showToast } from 'vant';
import { betting, coin } from "@/api/home.js"
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import _ from 'lodash';
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
const input = ref(0)
const dialogVisible = ref(false);
const props = defineProps({
    dialogVotePopup: {
        type: Boolean,
        require: false,
        default: false
    },
    data: {
        type: Object,
        default: {}
    },
})
watch(() => props.dialogVotePopup, (val) => {
    dialogVisible.value = val
}, { immediate: true })
const coinNum = ref(null)
const emits = defineEmits(['dialogClose', 'confirm'])
const addNumber = () => {
    if (coinNum.value > input.value) {
        input.value++
    }
}
const reduceNumber = () => {
    if (input.value > 0) {
        input.value--
    }
}
const changeInput = (value) => {
    console.log(value)
    if (value == 0) return
    // console.log(value)
    if (coinNum.value == null || coinNum.value == 0) {
    } else {
        if (value > coinNum.value) {
            input.value = coinNum.value
        }
    }

}
const handleInput = () => {
    console.log(input.value)
    if (input.value == '') {
        input.value = 0
    }
}
// 立即投票
const publish = _.debounce(() => {
    if (coinNum.value == 0) {
        showToast(t('vote.balance'));
    } else if (input.value == 0) {
        showToast(t('vote.voteTnfo'));
    } else {
        const query = {
            subId: props.data.subId,
            id: props.data.id,
            coin: input.value,
            token: countStore.loginData.token,
            vote: props.data.vote
        }
        betting(query)
            .then((res) => {
                console.log(res)
                if (res.code === 200) {
                    showToast(t('vote.voteSuccess'));
                    emits('confirm')
                } else if (res.code === 500) {
                    showToast(t('vote.statusEnd'));
                } else {
                    showToast(t('vote.voteFail'));
                }
            })
            .catch(function (error) {
                console.log(error);
                showToast(t('vote.voteFail'));
            });
    }
}, 1000, { leading: true, trailing: false })
// 取消按钮
const close = (() => {
    emits('dialogClose')
})
// 关闭dialog
const handleClose = (() => {
    emits('dialogClose')
})
const onClickPopup = (() => {

})
const onClickOverlay = (() => {

})
const getCoin = (() => {
    console.log('获取金币')
    const query = {
        token: countStore.loginData.token
    }
    coin(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                coinNum.value = res.data
            } else {
                showToast(t('toast.msgDataListFail'));
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('toast.msgDataListFail'));
        });
})
onMounted(() => {
    // 获取金币
    getCoin()
})
</script>
<style lang="scss" scoped>
:deep(.van-cell) {
    background: #B0BDC9;
    width: 226px;
    padding: 0;
    height: 40px;
}

:deep(.van-field__control) {
    text-align: center;
    color: #325D98;
    font-weight: 500;
    font-size: 33px;
}

:deep(.van-popup) {
    width: 750px;
    // height: 440px;
    background: linear-gradient(180deg, #EAF5FF 0%, #FFFFFF 100%);
    border-radius: 20px;
}

.deleteCon {
    padding: 0 42px;
    display: flex;
    flex-direction: column;
    align-items: center;

    .title {
        font-weight: 500;
        font-size: 32px;
        color: rgba(0, 0, 0, 0.9);
        margin-top: 50px;
    }

    .voteCon {
        width: 666px;
        height: 168px;
        background: rgba(210, 227, 243, 0.6);
        border-radius: 10px;
        margin-top: 34px;
        display: flex;
        flex-direction: column;
        align-items: center;

        .info {
            margin-top: 26px;
            font-size: var(--size_26);
            color: rgba(0, 0, 0, 0.7);
        }

        .input {
            margin-top: 27px;
            display: flex;
            align-items: center;

            img {
                width: 38px;
                height: 38px;
                margin: 0 12px;
            }
        }
    }

    .gold {
        padding-top: 10px;
        width: 100%;
        /* 左右两侧占位一样 */
        display: flex;
        align-items: center;
        justify-content: flex-end;
        font-size: 24px;
        color: #333;

        .num {
            color: #224C8B;
        }

        img {
            width: 38px;
            height: 38px;
            margin-right: 6px;
        }
    }

    .bottom {
        margin: 20px 0;
        display: flex;
        justify-content: center;
        align-items: center;
        width: 100%;

        .publish {
            // width: 152px;
            height: 68px;
            display: flex;
            align-items: center;
            text-align: center;
            color: #C4E5FF;
            font-size: var(--size_28);
            font-weight: var(--weight5);
            background: url("@/assets/images/index/publish.png") no-repeat;
            background-size: 100% 100%;
            padding: 0 60px;
        }


    }


    .close {
        position: absolute;
        top: 20px;
        right: 25px;
        z-index: 999;

        img {
            width: 50px;
            height: 50px;
        }
    }

}
</style>