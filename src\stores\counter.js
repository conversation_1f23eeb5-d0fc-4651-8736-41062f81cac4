import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
// 1042421690295910400
const useCounterStore = defineStore('counter',{
  state:()=>({
    // oneself 6666666666666666666 7777777777777777777 8888888888888888888
    // hBGTGGF53hRBzOkeulWGrmLQMioKS46CAF6tafivKqH4GvMMVOprHrbBQpRGWoY5LbIf++gF0AN4Fc7lt4TeeA==
    // 1041783274956914688
   loginRole:'',
   noReadMessageNum:0,
   likeAndcollectNum:0,
   aitAndConntentNum:0,
   replyNum:0,
   loginData:{
    avatarId: '',
    roleId: '',
    token: "",
    timestamp: 0
   },
  //  要查看谁的id 可以是自己也可以是其他人
  // seeUserId:'',
  seeUserRoleId:'',
  seeUserAvatarId:'',
  // 语言
  userLanguage:'',
  apiToken:'',
  clientWidth:0,
  audioType:'',
  dynamicData:[],
  favoritesData:[],
  likedData:[],
  weChatData:[],
  isCamera:false,
  appType:'',
  userData:{},
  baseUrl:'',
  deviceType:''
  })
 })
 export default useCounterStore
