const sp = {
    index:{
        title:'dinámico',
        menuItems: {
            Dynamic: 'Publicaciones',
            Favorites: '<PERSON>coger',
            Liked: 'Me gusta',
            Messages: '<PERSON><PERSON><PERSON>',
            weChat:'Momentos',
            myDynamic:'Mis publicaciones',
            myFavorites:'Mis favoritos',
            myLiked:'Mis me gusta',
            Hot:'Tendencias',
            Recommend:'Recomendar',
            New:'Último',
            Vote:'Adivinar&',
        },
        user:{
            praised:'Ganar me gusta',
            collect:'Recoger',
            chat:'Charla',
            addFriends:'Agregar amigos',
            friendRequest:'Solicitud de amistad',
            verificationMessage:'Información de verificación',
            sendNow:'Enviar inmediatamente',
            verificationMessagePlaceholder:'¡Cómo estás! Encantado de conocerte',
        },
        msg:{
            likeCollectTab:'Me gusta y favorito',
            commentAitTab:"Comentarios y {'@'}",
            reply:'Responder',
            likeCond:'Me gustó tu publicación',
            likeConc:'Te gustó tu comentario',
            likeConr:'Te gustó tu respuesta',
            collectCon:'He guardado tu publicación',
            commentCon:'Comenté en tu publicación',
            replyCon:'Respondido',
            aitCon:"{'@'}tú"
        }
    },
    detail:{
      replyNum:'Un total de %num% respuestas',
      seeMore:'Ver más',
      stop:'Plegar',
      originalText:'Texto original',
      translation:'Traducir',
      commentNum:'comentarios',
      send:'Enviar',
      reply:'Responder',
      replied:'Respondido',
      likeListTitle:'Me gusta de %num% amigos'
    },
    add:{
       title:'Publicar',
       input:'Por favor, ingresa el contenido de la publicación',
       topic:'Agregar tema',
       whoSee:{
          title:'¿A quién puedo mostrárselo?',
          all:'Todos',
          friend:'Los amigos pueden verlo',
          oneself:'Solo visible para ti',
       },
       publish:'Liberar',
       audioStart:'Haz clic para hablar',
       audioEnd:'Haz clic para terminar',
       searchPlaceholder:'Buscar a tus amigos',
       confirm:'Confirmar',
       audioPermission:'Por favor, abre primero el permiso del micrófono',
       imagePermission:'Por favor, habilita primero los permisos de la cámara',
       aitUser:"{'@'}usuario"
    },
    report:{
       next:'Siguiente paso',
       confirmReport:'Confirmar el informe',
       placeholder:'Por favor ingresa contenido',
       detailTitle:'Descripción detallada',
       imgTitle:'Evidencia en imagen',
       reportSuccess:'Informe exitoso',
       reportFail:'Informe fallido',
       reportSuccessInfo:'Después de la presentación, la plataforma verificará y procesará activamente; ¡gracias por su esfuerzo para mantener el entorno social!',
       reportPublish:'Dinámicas de retorno',
       reportOneTitleAfterD:'Estás informando la publicación de %name%',
       reportOneTitleAfterC:'Estás informando de comentarios de %name%',
       reportOneTitleAfterR:'Estás informando de una respuesta %name%',
       reportTwoTitle:'Has seleccionado %title%',
       reportEndTitleAfterD:'Su informe de %name% pertenece a %title%',
       reportEndTitleAfterC:'%name% su informe pertenece a %title%',
       reportEndTitleAfterR:'Su informe de respuesta %name% pertenece a %title%',
    },
    tooltip:{
        delete:'Eliminar',
        modify:'Modificar',
        cancelCollect:'Eliminar de favoritos',
        report:'Reportar',
        block:'Bloquear',
    },
    delete:{
      deleteCon:'¿Estás seguro de que quieres eliminar este contenido?',
      deleteCancel:'Cancelar',
      deleteConfirm:'Confirmar',
      blockCon:'¿Estás seguro de querer bloquear?',
    },
    toast:{
        likeSuccess:'Me gusta exitoso',
        likeCancel:'Me gusta ha sido cancelado',
        likeFail:'Error al dar me gusta',
        collectSuccess:'Favorito exitoso',
        collectCancel:'Eliminado de favoritos',
        collectFail:'Error al agregar a favoritos',
        publishSuccess:'Publicación exitosa',
        publishFail:'Error en la publicación',
        modifySuccess:'La modificación ha sido exitosa',
        topicInfo:'Puedes elegir hasta 5 temas',
        aitInfo:"Hasta {'@'}5 usuarios",
        ait:"Por favor, ingrese al menos 1 carácter antes de {'@'}",
        dynamicInput:'Por favor, sube al menos uno de los siguientes: contenido dinámico, imágenes o videos',
        nextInfo:'Por favor, seleccione una opción primero',
        reportSuccess:'Informe exitoso',
        reportFail:'Informe fallido',
        audioTextSuccess:'Conversión de voz a texto exitosa',
        audioTextFail:'Conversión de voz a texto fallida',
        translationSuccess:'Traducción exitosa',
        translationFail:'Traducción fallida',
        uploadImageFail:'Error al subir',
        deleteSuccess:'Eliminación exitosa',
        deleteFail:'Eliminación fallida',
        // 新加
        imageLimit:'El tamaño del archivo no puede exceder %num%MB',
        imageNum:'Hasta 9 imágenes se pueden cargar',
        uploadPrompt:'Por favor, haga clic para subir imágenes y videos',
        filePrompt:'(Soporte en formatos de archivo Doc, docx y pdf, y el tamaño de archivo no puede exceder de 5 mb)',
        imageBefore:'No más de 4 MB para una sola imagen',
        imageShowToast:'Cargar archivos demasiado grandes',
        audioFail:'Error al finalizar la grabación',
        collectCancelFail:'Cancelación fallida',
        collectCancelSuccess:'Cancelación exitosa',
        dynamicFail:'La publicación no existe',
        addCommentViolation:'El contenido que enviaste se sospecha que viola las regulaciones, por favor modifícalo y vuelve a enviarlo. Modifica y vuelve a enviar.',
        addCommentFail:'Error al agregar comentario',
        addReplyFail:'Error al agregar respuesta',
        addDynamicViolation:'El contenido del "post" que enviaste se sospecha que viola las regulaciones, por favor modifícalo y vuelve a enviarlo.',
        addTopicViolation:'El contenido del "tema" que enviaste se sospecha que viola las regulaciones, por favor modifícalo y vuelve a enviarlo.',
        addImageViolation:'El contenido de la "imagen" que enviaste se sospecha que viola las regulaciones, por favor modifícalo y vuelve a enviarlo.',
        topicCon:'El contenido del tema no puede estar vacío',
        getMsgFail:'Error al recuperar información',
        loginFail:'Error de inicio de sesión',
        aitInfoPermission:'Actualmente visible solo para ti',
        alreadyReport:'Has reportado múltiples veces, por favor espera la retroalimentación de la plataforma ',
        commentAfterDelete:'Comentario eliminado',
        replyAfterDelete:'Respuesta eliminada',
        msgDataListFail:'Error en la obtención de datos',
        videoLimit:'El vídeo no debe superar los 25 MB',
        videoPrompt:'Se puede subir un máximo de 1 vídeo',
        videoToast:'Solo se pueden subir imágenes o vídeos',
        imageTitle:'Subir imagen',
        videoTitle:'Subir vídeo',
        applySuccess:'Solicitud enviada con éxito',
        applyFail:'Fallo al enviar la solicitud',
        blacklistPrompt:'No se pueden agregar amigos desde la lista negra',
        friendNumPrompt:'El número de amigos del otro usuario está completo',
        myNumPrompt:'El número actual de amigos está completo',
        failedPrompt:'Error en los parámetros',
        alignPrompt:'Ya has agregado a esta persona como amigo, no puedes volver a enviar la solicitud',
        applyMyPrompt:'No puedes agregarte a ti mismo',
        alignApply:'Ya has enviado una solicitud de amistad. Puedes enviar otra en 48 horas',
        blockSuccess:'El usuario ha sido agregado a la lista negra',
        blockFail:'El bloqueo ha fallado',
        blockListFull:'La lista de bloqueo está llena',
        checkAgreementPrompt:'Usted no está de acuerdo con 《Declaración de publicación de contenido》 y no puede publicar la dinámica',
        AgreementFile:'Ha leído y aceptado el documento',
        fileTitle:'《Declaración de publicación de contenido》',
        sameLanguagePrompt:'Actualmente en el mismo idioma, no se necesita traducción',

    },
    vote:{
        voteProgress:'En progreso',
        voteEnd:'Terminado',
        voteSettle:'Liquidado',
        oneselfNum:'Ha votado',
        voteNum:'{num} Münze',
        timeName:'Tiempo restante',
        allNum:'Número total de monedas',
        participateInVoting:'Número total de monedas',
        getCoins:'Esta vez, has ganado {num} monedas',
        voteBtn:'Elegir',
        voteTitle:'Votar por {num}',
        inputInfo:'Por favor, seleccione la cantidad',
        voteConfirm:'Confirmar',
        voteSuccess:'Éxito',
        voteFail:'Fracaso',
        statusEnd:'El evento ha terminado',
        voteTnfo:'El número mínimo de monedas requerido para participar en el evento es de 1',
        hold:'Tener',
        balance:'Su saldo actual de cuenta es insuficiente. Por favor, recargue a tiempo',
        remainingTimeData:'{days} Tage {hours} Stunden {minutes} Minuten {seconds} Sekunden',
        questionInfo:'Todas las monedas de los usuarios irán al bote de premios de este evento, y los usuarios que adivinen correctamente se repartirán todas las 【monedas de tiempo-espacio】 del bote de premios de acuerdo con la cantidad de monedas que hayan adivinado.',
    },
    video:{
        videoIndex:'Di algo...',
        videoDetail:'Página de detalles',
        videoTitle:'Vídeo',
    },
    empty:{
        comment:'Sin comentarios aún',
        list:'No hay datos disponibles',
        content:'No hay contenido disponible',
        message:'No hay mensajes disponibles'
    }
}

export default sp;

