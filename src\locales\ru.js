const ru = {
    index:{
        title:'Главная страница',
        menuItems: {
            Dynamic: 'Посты',
            Favorites: 'Собрать',
            Liked: 'Понравилось',
            Messages: 'Сообщение',
            weChat:'Моменты',
            myDynamic:'Мои посты',
            myFavorites:'Мои избранные',
            myLiked:'Мои лайки',
            Hot:'Тренды',
            Recommend:'Рекомендовать',
            New:'Последний',
            Vote:'Угадать&Получить',
        },
        user:{
            praised:'Получить лайки',
            collect:'Собрать',
            chat:'Чат',
            addFriends:'Добавить друзей',
            friendRequest:'Заявка на дружбу',
            verificationMessage:'Информация для проверки',
            sendNow:'Отправить немедленно',
            verificationMessagePlaceholder:'Каких друзей вы хотите найти?',
        },
        msg:{
            likeCollectTab:'Лайкнуть и добавить в избранное',
            commentAitTab:"Комментарии и {'@'}",
            reply:'Ответить',
            likeCond:'Мне понравился твой пост',
            likeConc:'Понравился ваш комментарий',
            likeConr:'Понравился ваш ответ',
            collectCon:'Я сохранил твой пост',
            commentCon:'Прокомментировал твой пост',
            replyCon:'Ответил',
            aitCon:"{'@'}вам"
        }
    },
    detail:{
      replyNum:'Всего %num% ответов',
      seeMore:'Смотреть больше',
      stop:'Свернуть',
      originalText:'Оригинальный текст',
      translation:'Перевести',
      commentNum:'комментариев',
      send:'Отправить',
      reply:'Ответить',
      replied:'Ответил',
    //   have:'已有',
      likeListTitle:'Лайки от %num%  друзей'
    },
    add:{
       title:'Опубликовать',
       input:'Пожалуйста, введите содержимое поста',
       topic:'Добавить тему',
       whoSee:{
          title:'Кому я могу это показать',
          all:'Всем',
          friend:'Друзья могут видеть',
          oneself:'Только вам видно',
       },
       publish:'Опубликовать',
       audioStart:'Нажмите, чтобы говорить',
       audioEnd:'Нажмите, чтобы закончить',
       searchPlaceholder:'Искать своих друзей',
       confirm:'Подтвердить',
       audioPermission:'Пожалуйста, сначала откройте разрешение на микрофон',
       imagePermission:'Пожалуйста, сначала включите разрешения на камеру',
       aitUser:"{'@'}пользователь"
    },
    report:{
       next:'Следующий шаг',
       confirmReport:'Подтвердите отчет',
       placeholder:'Пожалуйста, введите содержимое',
       detailTitle:'Подробное описание',
       imgTitle:'Доказательства изображения',
       reportSuccess:'Отчет успешен',
       reportFail:'Отчет не удался',
       reportSuccessInfo:'После отправки платформа активно проверит и обработает; спасибо за ваши усилия по поддержанию социальной среды!',
       reportPublish:'Возврат динамики',
       reportOneTitleAfterD:'вы сообщаете о сообщении %name%',
       reportOneTitleAfterC:'Вы сообщаете о комментарии %name%',
       reportOneTitleAfterR:'вы сообщаете об ответе %name%',
       reportTwoTitle:'Вы выбрали %title%',
       reportEndTitleAfterD:'ваш отчет о посту %name% принадлежит %title%',
       reportEndTitleAfterC:'ваш отчет о комментариях %name% принадлежит %title%',
       reportEndTitleAfterR:'ваш отчет о ответах %name% принадлежит %title%',
    },
    tooltip:{
        delete:'Удалить',
        modify:'Изменить',
        cancelCollect:'Убрать из избранного',
        report:'Сообщить',
        block:'Заблокировать',
    },
    delete:{
      deleteCon:'Вы уверены, что хотите удалить этот контент?',
      deleteCancel:'Отменить',
      deleteConfirm:'Подтвердить',
      blockCon:'Вы уверены, что хотите заблокировать?',
    },
    toast:{
        likeSuccess:'Успешно понравилось',
        likeCancel:'Лайк был отменен',
        likeFail:'Не удалось поставить лайк',
        collectSuccess:'Избранное успешно',
        collectCancel:'Убрано из избранного',
        collectFail:'Не удалось добавить в избранное',
        publishSuccess:'Пост успешно',
        publishFail:'Не удалось опубликовать пост',
        modifySuccess:'Изменение успешно',
        topicInfo:'Вы можете выбрать до 5 тем',
        aitInfo:"До {'@'}5 пользователей",
        ait:"Пожалуйста, введите хотя бы 1 символ перед {'@'}",
        dynamicInput:'Загрузите, пожалуйста, по крайней мере один из следующих элементов: динамический контент, изображения или видео',
        nextInfo:'Пожалуйста, сначала выберите один вариант',
        reportSuccess:'Отчет успешен',
        reportFail:'Отчет не удался',
        audioTextSuccess:'Преобразование голоса в текст успешно',
        audioTextFail:'Преобразование голоса в текст не удалось',
        translationSuccess:'Перевод успешен',
        translationFail:'Перевод не удался',
        uploadImageFail:'Ошибка загрузки',
        deleteSuccess:'Удаление успешно',
        deleteFail:'Удаление не удалось',
        // 新加
        imageLimit:'Размер файла не может превышать %num%МБ',
        imageNum:'можно загрузить до 9 изображений',
        uploadPrompt:'Пожалуйста, нажмите, чтобы загрузить картинку и видео',
        filePrompt:'(Поддерживаются форматы файлов Doc, docx и pdf, размер файла не может превышать 5 мб)',
        imageBefore:'не более 4 МБ для одного изображения',
        imageShowToast:'Загруженный файл слишком большой',
        audioFail:'Ошибка завершения записи', 
        collectCancelFail:'Отмена не удалась',
        collectCancelSuccess:'Отмена успешна',
        dynamicFail:'Пост не существует',
        addCommentViolation:'Содержимое, которое вы представили, подозревается в нарушении правил, пожалуйста, измените и отправьте снова. Измените и отправьте снова.',
        addCommentFail:'Не удалось добавить комментарий',
        addReplyFail:'Не удалось добавить ответ',
        addDynamicViolation:'Содержимое "поста", которое вы отправили, подозревается в нарушении правил, пожалуйста, измените его и отправьте снова.',
        addTopicViolation:'Содержимое "темы", которое вы отправили, подозревается в нарушении правил, пожалуйста, измените его и отправьте снова.',
        addImageViolation:'Содержимое "изображения", которое вы отправили, подозревается в нарушении правил, пожалуйста, измените его и отправьте снова.',
        topicCon:'Содержимое темы не может быть пустым',
        getMsgFail:'Не удалось получить информацию',
        loginFail:'Ошибка входа',
        aitInfoPermission:'В данный момент видно только вам',
        alreadyReport:'Вы сообщили несколько раз, пожалуйста, дождитесь обратной связи от платформы',
        commentAfterDelete:'Комментарии удалены',
        replyAfterDelete:'Ответ удален',
        msgDataListFail:'Ошибка получения данных',
        videoLimit:'Видео не должно превышать 25 МБ',
        videoPrompt:'Можно загрузить не более 1 видео.',
        videoToast:'Можно загружать только изображения или видео.',
        imageTitle:'Загрузить изображение.',
        videoTitle:'Загрузить видео.',
        applySuccess:'Заявка успешно отправлена',
        applyFail:'Не удалось отправить заявку',
        blacklistPrompt:'Нельзя добавлять друзей из черного списка',
        friendNumPrompt:'Количество друзей у другого пользователя уже достигло максимума',
        myNumPrompt:'Количество текущих друзей уже достигло максимума',
        failedPrompt:'Ошибка параметра',
        alignPrompt:'Вы уже добавили этого человека в друзья, повторная заявка невозможна',
        applyMyPrompt:'Нельзя добавить себя в друзья',
        alignApply:'Ты уже отправил запрос на дружбу. Сможешь отправить еще один через 48 часов',
        blockSuccess:'Пользователь был добавлен в черный список',
        blockFail:'Блокировка не удалась',
        blockListFull:'Список блокировки полон',
        checkAgreementPrompt:'Вы не согласны с 《Объявление о публикации контента》 и не можете опубликовать динамику',
        AgreementFile:'Вы прочитали и согласились с этим документом',
        fileTitle:'《Объявление о публикации контента》',
        sameLanguagePrompt:'В настоящее время используется один и тот же язык, перевод не требуется',

    },
    vote:{
        voteProgress:'В процессе',
        voteEnd:'Завершено',
        voteSettle:'Оплачено',
        oneselfNum:'Голосовал',
        voteNum:'{num} Монета',
        timeName:'Оставшееся время',
        allNum:'Общее количество монет',
        participateInVoting:'Общее количество игроков:',
        getCoins:'В этот раз вы заработали {num} монет',
        voteBtn:'Выбирать',
        voteTitle:'Голосовать за {num}',
        inputInfo:'Пожалуйста, выберите количество',
        voteConfirm:'Подтвердить',
        voteSuccess:'Успех',
        voteFail:'Неудача',
        statusEnd:'Событие завершено',
        voteTnfo:'Минимальное количество монет, необходимых для участия в мероприятии, составляет 1',
        hold:'Иметь',
        balance:'Ваш текущий баланс недостаточен. Пожалуйста, пополните счет вовремя',
        remainingTimeData:'{days} дней {hours} часов {minutes} мин {seconds} сек',
        questionInfo:'Все монеты пользователя пойдут в призовой фонд этого мероприятия, и пользователи, которые правильно угадают, поделят все 【тайм-спейс монеты】 в призовом фонде в соответствии с количеством угаданных монет.',
    },
    video:{
        videoIndex:'Скажите что-нибудь...',
        videoDetail:'Страница деталей',
        videoTitle:'Видео',
    },
    empty:{
        comment:'Комментариев пока нет',
        list:'Данные недоступны',
        content:'Содержимое недоступно',
        message:'Нет доступных сообщений'
    }
}

export default ru;

