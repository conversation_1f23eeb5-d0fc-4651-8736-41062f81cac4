const pt = {
    index:{
        title:'Página inicial de postagens',
        menuItems: {
            Dynamic: 'Postagens',
            Favorites: 'Cole<PERSON>',
            Liked: 'Curtidas',
            Messages: 'Mensagem',
            weChat:'Momentos',
            myDynamic:'Minhas postagens',
            myFavorites:'Meus favoritos',
            myLiked:'Minhas curtidas',
            Hot:'Tendências',
            Recommend:'Recomendar',
            New:'Mais recente',
            Vote:'Adivinhe&Ganhar',
        },
        user:{
            praised:'Ganhar curtidas',
            collect:'<PERSON><PERSON>',
            chat:'Conversar',
            addFriends:'Adicionar amigos',
            friendRequest:'Solicitação de amizade',
            verificationMessage:'Informação de verificação',
            sendNow:'Enviar imediatamente',
            verificationMessagePlaceholder:'Como você está! Prazer em conhecê-lo',
        },
        msg:{
            likeCollectTab:'Curtir e favoritar',
            commentAitTab:"Comentários e {'@'}",
            reply:'Responder',
            likeCond:'Eu curti sua postagem',
            likeConc:'Gostei do seu comentário',
            likeConr:'Goste<PERSON> da sua resposta',
            collectCon:'Eu salvei sua postagem',
            commentCon:'Comentei na sua postagem',
            replyCon:'Respondeu',
            aitCon:"{'@'}você"
        }
    },
    detail:{
      replyNum:'Um total de %num% respostas',
      seeMore:'Ver mais',
      stop:'Dobrar',
      originalText:'Texto original',
      translation:'Traduzir',
      commentNum:'comentários',
      send:'Enviar',
      reply:'Responder',
      replied:'Respondeu',
      likeListTitle:'Curtidas de %num% amigos'
    },
    add:{
       title:'Postar',
       input:'Por favor, insira o conteúdo do post',
       topic:'Adicionar tópico',
       whoSee:{
          title:'Para quem posso mostrar isso',
          all:'Todos',
          friend:'Amigos podem ver',
          oneself:'Somente visível para você',
       },
       publish:'Liberar',
       audioStart:'Clique para falar',
       audioEnd:'Clique para encerrar',
       searchPlaceholder:'Procure seus amigos',
       confirm:'Confirmar',
       audioPermission:'Por favor, abra a permissão do microfone primeiro',
       imagePermission:'Por favor, habilite as permissões da câmera primeiro',
       aitUser:"{'@'}usuário"
    },
    report:{
       next:'Próximo passo',
       confirmReport:'Confirmar o relatório',
       placeholder:'Por favor, insira o conteúdo',
       detailTitle:'Descrição detalhada',
       imgTitle:'Evidência de imagem',
       reportSuccess:'Relatório bem-sucedido',
       reportFail:'Relatório falhou',
       reportSuccessInfo:'Após a submissão, a plataforma irá verificar e processar ativamente; obrigado pelo seu esforço em manter o ambiente social!',
       reportPublish:'Retornar dinâmicas',
       reportOneTitleAfterD:'Você está relatando um post %name%',
       reportOneTitleAfterC:'Você está relatando comentários de %name%',
       reportOneTitleAfterR:'Você está relatando uma resposta de %name%',
       reportTwoTitle:'Você escolheu %title%',
       reportEndTitleAfterD:'Seu relatório de postagem %name% pertence a %title%',
       reportEndTitleAfterC:'Seu relatório para comentários de %name% pertence a %title%',
       reportEndTitleAfterR:'Seu relatório de resposta %name% pertence a %title%',
    },
    tooltip:{
        delete:'Excluir',
        modify:'Modificar',
        cancelCollect:'Remover dos favoritos',
        report:'Relatar',
        block:'Bloquear',
    },
    delete:{
      deleteCon:'Você tem certeza de que deseja excluir este conteúdo?',
      deleteCancel:'Cancelar',
      deleteConfirm:'Confirmar',
      blockCon:'Quer mesmo bloquear?',
    },
    toast:{
        likeSuccess:'Curtir bem-sucedido',
        likeCancel:'Curtida foi cancelada',
        likeFail:'Falha na curtida',
        collectSuccess:'Favorito com sucesso',
        collectCancel:'Removido dos favoritos',
        collectFail:'Falha ao favoritar',
        publishSuccess:'Postagem realizada com sucesso',
        publishFail:'Falha na postagem',
        modifySuccess:'Modificação bem-sucedida',
        topicInfo:'Você pode escolher até 5 tópicos',
        aitInfo:"Até {'@'}5 usuários",
        ait:"Por favor, insira pelo menos 1 caractere antes de {'@'}",
        dynamicInput:'Por favor, faça o upload de pelo menos um dos seguintes: conteúdo dinâmico, imagens ou vídeos',
        nextInfo:'Por favor, selecione uma opção primeiro',
        reportSuccess:'Relatório bem-sucedido',
        reportFail:'Relatório falhou',
        audioTextSuccess:'Voz para texto bem-sucedido',
        audioTextFail:'Voz para texto falhou',
        translationSuccess:'Tradução bem-sucedida',
        translationFail:'Tradução falhou',
        uploadImageFail:'Upload falhou',
        deleteSuccess:'Exclusão bem-sucedida',
        deleteFail:'Exclusão falhou',
        // 新加
        imageLimit:'O tamanho do arquivo não pode exceder %num%MB',
        imageNum:'Até 9 imagens podem ser carregadas',
        uploadPrompt:'Por favor, clique para carregar fotos & vídeos',
        filePrompt:'(suportado com formatos de arquivo Doc, docx e pdf, o tamanho do arquivo não pode exceder 5 mb)',
        imageBefore:'Uma única imagem não excede 4 mb',
        imageShowToast:'O arquivo de upload é muito grande',
        audioFail:'Erro ao encerrar a gravação',
        collectCancelFail:'Cancelamento falhou',
        collectCancelSuccess:'Cancelamento bem-sucedido',
        dynamicFail:'Postagem não existe',
        addCommentViolation:'O conteúdo que você enviou é suspeito de violar regulamentos, por favor, modifique e reenvie. Modifique e reenvie.',
        addCommentFail:'Falha ao adicionar comentário',
        addReplyFail:'Falha ao adicionar resposta',
        addDynamicViolation:'O conteúdo do "post" que você enviou é suspeito de violar regulamentos, por favor, modifique-o e reenvie.',
        addTopicViolation:'O conteúdo do "tópico" que você enviou é suspeito de violar regulamentos, por favor, modifique-o e reenvie.',
        addImageViolation:'O conteúdo da "imagem" que você enviou é suspeito de violar regulamentos, por favor, modifique-o e reenvie.',
        topicCon:'O conteúdo do tópico não pode estar vazio',
        getMsgFail:'Falha ao recuperar informações',
        loginFail:'Falha ao fazer login',
        aitInfoPermission:'Atualmente visível apenas para você',
        alreadyReport:'Você relatou várias vezes, por favor, aguarde o feedback da plataforma',
        commentAfterDelete:'Comentário removido',
        replyAfterDelete:'Resposta eliminada',
        msgDataListFail:'Falha na aquisição de dados',
        videoLimit:'O vídeo não pode exceder 25 MB',
        videoPrompt:'É possível enviar no máximo 1 vídeo',
        videoToast:'Apenas imagens ou vídeos podem ser enviados',
        imageTitle:'Enviar imagem',
        videoTitle:'Enviar vídeo',
        applySuccess:'Solicitação enviada com sucesso',
        applyFail:'Falha ao enviar solicitação',
        blacklistPrompt:'Não é possível adicionar amigos da lista negra',
        friendNumPrompt:'O número de amigos do outro usuário está completo',
        myNumPrompt:'O número atual de amigos está completo',
        failedPrompt:'Erro nos parâmetros',
        alignPrompt:'Você já adicionou essa pessoa como amigo, não é possível enviar a solicitação novamente',
        applyMyPrompt:'O vídeo não pode exceder 25 MB',
        alignApply:'Você já enviou um pedido de amizade. Pode enviar outro em 48 horas',
        blockSuccess:'O usuário foi adicionado à lista negra',
        blockFail:'Falha ao bloquear',
        blockListFull:'A lista de bloqueio está cheia',
        checkAgreementPrompt:'Você não concordou com《Contrato de postagem de conteúdo》e não pode postar a dinâmica',
        AgreementFile:'Você leu e aceitou o documento',
        fileTitle:'《Declaração de publicação de conteúdo》',
        sameLanguagePrompt:'Atualmente no mesmo idioma, não é necessário traduzir',

    },
    vote:{
        voteProgress:'Em progresso',
        voteEnd:'Terminado',
        voteSettle:'Liquidado',
        oneselfNum:'Já votou',
        voteNum:'{num} Moeda',
        timeName:'Tiempo restante',
        allNum:'Número total de moedas',
        participateInVoting:'Número total de jogadores:',
        getCoins:'Desta vez, você ganhou {num} moedas',
        voteBtn:'Escolher',
        voteTitle:'Votar em {num}',
        inputInfo:'Por favor, selecione a quantidade',
        voteConfirm:'Confirmar',
        voteSuccess:'Sucesso',
        voteFail:'Falha',
        statusEnd:'O evento terminou',
        voteTnfo:'O número mínimo de moedas necessário para participar no evento é de 1',
        hold:'Ter',
        balance:'Seu saldo atual da conta é insuficiente. Por favor, recarregue a tempo',
        remainingTimeData:'{days} dias {hours} horas {minutes} min {seconds} segs',
        questionInfo:'Todas as moedas dos usuários serão colocadas no pool de prêmios deste evento, e os usuários que adivinharem corretamente dividirão todas as 【moedas de tempo-espaço】 no pool de prêmios de acordo com o número de moedas que eles adivinharam.',
    },
    video:{
        videoIndex:'Diga algo...',
        videoDetail:'Página de detalhes',
        videoTitle:'Vídeo',
    },
    empty:{
        comment:'Nenhum comentário ainda',
        list:'Nenhum dado disponível',
        content:'Nenhum conteúdo disponível',
        message:'Nenhuma mensagem disponível'
    }
}

export default pt;

