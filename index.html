<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <link rel="icon" href="/favicon.ico">
  <!--  QQ 浏览器横屏-->
  <!-- <meta name="x5-orientation" content="landscape"> -->
  <!-- UC 浏览器横屏 -->
  <!-- <meta name="screen-orientation" content="landscape"> -->
  <!--      这里的 作用是 让 页面的 宽度 适配 手机屏幕的 宽度，这样写 就能使 html 的 width 等于 对应手机 屏幕的 宽度。另外 还阻止用户 缩放 界面-->
  <!--      目的是 让界面显示 更加适应 手机屏幕-->
  <meta name="viewport"
    content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0">
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1.0"> -->
  <link rel="preload" href="./src/assets/images/index/leftMenuBg.webp" as="image" importance="high">
  <!-- <link rel="preload" href="./src/assets/base.scss" as="image" importance="high">
  <link rel="preload" href="./src/assets/css/index/menu.scss" as="image" importance="high"> -->
  <title>Vite App</title>
  <style>
    /* 竖屏 */
    /* @media screen and (orientation: portrait) {
      html {
        transform: rotate(90deg) translate(0, -100%);
        transform-origin: 0 0;
        width: 100vh;
        height: 100vw;
        overflow: hidden;
      }

    }

    @supports (bottom: env(safe-area-inset-bottom)) {
      body,
      .iphonex {
        padding-bottom: env(safe-area-inset-bottom);
        padding-bottom: constant(safe-area-inset-bottom);
        padding-top: env(safe-area-inset-top);
        padding-top: constant(safe-area-inset-top);
      }

    } */
  </style>
</head>

<body style="height: 100%;">
  <div id="app" v-cloak>
  </div>
  <script type="module" src="/src/main.js">


  </script>
  <script>
  </script>
</body>

</html>