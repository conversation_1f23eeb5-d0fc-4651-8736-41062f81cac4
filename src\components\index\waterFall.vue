<template>
    <div class="itemCon" @click="clickComment">
        <div class="top">
            <avatar :url="data.avatart" :userId="data.userId"></avatar>
            <div class="info">
                <div class="name">{{ data.userName }}</div>
                <div class="time">{{ time(data.createTime) }}</div>
            </div>
            <popover v-if="tooltipType" imgType="dynamic" :data="data" :tooltipType="tooltipType"></popover>
        </div>
        <div class="content" @click.stop="gotoDetail(data.id)">
            <div class="topic" v-if="data.tagList.length">
                <span v-for="item in data.tagList" :key="item">#{{ item }}</span>
            </div>
            <div class="topic" v-if="data.aitList.length">
                <span v-for="item in data.aitList" :key="item">@{{ item.name }}</span>
            </div>
            <p class="text-box">{{ data.content }}</p>
            <div class="seeFile" v-if="data.attachmentUrl">
                <img src="@/assets/images/index/listFile.png" />
                <div class="name">{{ data.attachmentName }}</div>
                <div class="size">{{ data.attachmentSize }}</div>
            </div>
            <div class="img" v-if="data.imageList.length > 0">
                <img
                    :src="data.imageList[0]"
                    :class="{ 'image-loaded': isNormalImageLoaded }"
                    @load="normalImageLoaded"
                    @error="normalImageError"
                    loading="lazy"
                />
                <div v-if="!isNormalImageLoaded" class="image-placeholder">
                    <div class="loading-spinner"></div>
                </div>
            </div>
            <div v-if="data.videoFrame" class="videoList">
                <div class="videoPosterContainer">
                    <img class="videoPoster" :src="data.videoFrame" @load="imageLoaded" />
                    <div class="videoBtn" v-if="isImageLoaded">
                        <img src="@/assets/images/video/videoBtn.webp" />
                    </div>

                </div>
            </div>
            <div class="bottom">
                <div class="bottomCon">
                    <div class="bottomItem">
                        <img v-if="data.isLike" src="@/assets/images/index/selectLove.png"
                            @click.stop="changeLike(data.id)" />
                        <img v-else src="@/assets/images/index/noSelectLove.png" @click.stop="changeLike(data.id)" />
                        <div>{{ data.likeNum }}</div>
                    </div>
                    <div class="bottomItem">
                        <img src="@/assets/images/index/comment.png" />
                        <div>{{ data.commentNum }}</div>
                    </div>
                    <div class="bottomItem">
                        <img v-if="data.isCollect" src="@/assets/images/index/selectCollect.png"
                            @click.stop="changeCollect(data.id)" />
                        <img v-else src="@/assets/images/index/noSelectCollect.png"
                            @click.stop="changeCollect(data.id)" />
                        <div>{{ data.collectNum }}</div>
                    </div>
                </div>
            </div>
            <!-- && tooltipType == 'myFriend' -->
            <div class="like" v-if="data.likeList.length">
                <img src="@/assets/images/index/likeList.png" />
                <div class="likeList">
                    <template v-for="(item, index) in data.likeList" :key="index">
                        <span class="like-item" :class="item.trim() == '' ? 'showSpace' : ''">
                            <span class="item-text" dir="auto">{{ item }}</span>
                        </span>
                        <span v-if="index !== data.likeList.length - 1" class="separator">、</span>
                    </template>
                </div>
            </div>
        </div>
    </div>

</template>
<script setup>
import { ymdDianTime } from "@/utils/time.js"
import { ref, reactive, onMounted, computed, watch, onUnmounted, defineAsyncComponent } from 'vue';
import { showToast } from 'vant';
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
import { isSeleteLike, isSeleteCollect } from "@/assets/js/select.js"
import { like, collect } from "@/api/home.js"
import emitter from '@/utils/mitt.js'
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
const props = defineProps({
    data: {
        type: Object,
        default: () => {
            return {};
        },
    },
    index: {
        type: Number,
        require: false,
        default: null
    },
    dynamicType: {
        type: String,
        default: ''
    },
    type: {
        type: String,
        default: ''
    },
});
const tooltipType = ref('')
const imgCollectUrl = ref()
const imgLoveUrl = ref()
const isImageLoaded = ref(false)
const isNormalImageLoaded = ref(false)
const likeList = ref(['大捷大捷大捷', '    ', '大捷水水水水水捷李四李四', '  ', '李四'])
onMounted(() => {
    // console.log(props.dynamicType)
    // console.log(props.data)
    // 判断tooltipType类型
    if (props.dynamicType == 'other') {
        //其他人 都是举报
        tooltipType.value = 'otherDynamic'
    } else {
        //自己 进一步判断
        if (props.type == 'dynamic') {
            tooltipType.value = 'myDynamic'
        } else if (props.type == 'collect') {
            tooltipType.value = 'myCollect'
        } else if (props.type == 'like') {
            tooltipType.value = 'myLike'
        } else {
            tooltipType.value = 'myFriend'
        }
    }
    // islike 0 false 1 true
    // props.data.isLike ? imgLoveUrl.value = isSeleteLike.selete : imgLoveUrl.value = isSeleteLike.noselete
    props.data.isCollect ? imgCollectUrl.value = isSeleteCollect.selete : imgCollectUrl.value = isSeleteCollect.noselete
})
// 计算时间
const time = ((data) => {
    return ymdDianTime(data)
})
const clickComment = (event) => {
    // 不做任何阻止事件冒泡的操作，让事件继续传播
}
// 图片加载完成
const imageLoaded = (() => {
    // console.log('图片加载完成');
    isImageLoaded.value = true
    // 触发事件，通知父组件图片已加载完成
    emitter.emit('imageLoaded', props.data.id);
})

// 普通图片加载完成
const normalImageLoaded = (() => {
    console.log('普通图片加载完成');
    isNormalImageLoaded.value = true;

    // 延迟触发重绘，确保图片完全渲染
    setTimeout(() => {
        // 触发事件，通知父组件图片已加载完成，需要重绘瀑布流
        emitter.emit('imageLoaded', props.data.id);
    }, 100);
})

// 普通图片加载错误
const normalImageError = (() => {
    console.log('普通图片加载失败');
    isNormalImageLoaded.value = true; // 即使失败也设置为已加载，避免一直显示loading

    // 触发重绘事件
    setTimeout(() => {
        emitter.emit('imageLoaded', props.data.id);
    }, 100);
})
const emits = defineEmits(['showDetail'])
// 向父组件传递popup显示事件
const gotoDetail = (() => {
    console.log('点击进入详情')
    console.log(tooltipType.value)
    emits('showDetail', props.data, tooltipType.value, true, props.index)
})
// 点击点赞
const changeLike = (id) => {
    const query = {
        dyNamicsId: id,
        likeType: '0'
    }
    like(query)
        .then((res) => {
            console.log(res)
            if (res.code == 200) {
                console.log(props.data.isLike)
                console.log(props.data)
                emitter.emit('likeListSuccess')
                if (props.data.isLike) {
                    props.data.likeNum--
                    props.data.isLike = 0
                    console.log(t('toast.likeCancel'))
                    showToast(t('toast.likeCancel'));
                    imgLoveUrl.value = isSeleteLike.noselete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('likeCancel', { like_count: 1 });
                } else {
                    props.data.likeNum++
                    props.data.isLike = 1
                    console.log(t('toast.likeSuccess'))
                    showToast(t('toast.likeSuccess'));
                    imgLoveUrl.value = isSeleteLike.selete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('like', { like_count: 1 });
                }
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('toast.likeFail'))
        });
};
// 点击收藏
const changeCollect = (id) => {
    const query = {
        dyNamicsId: id,
    }
    collect(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                // 在详情收藏 通知对应列表更新状态
                emitter.emit('collectSuccess')
                if (props.data.isCollect) {
                    props.data.collectNum--
                    props.data.isCollect = 0
                    // showToast('已取消点赞');
                    showToast(t('toast.collectCancel'));
                    imgCollectUrl.value = isSeleteCollect.noselete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('collectCancel', { collect_count: 1 });
                } else {
                    props.data.collectNum++
                    props.data.isCollect = 1
                    showToast(t('toast.collectSuccess'));
                    imgCollectUrl.value = isSeleteCollect.selete
                    // 使用数数的 SDK 记录点击事件
                    ta.track('collect', { collect_count: 1 });
                }
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('collectFail.likeFail'))
        });
};
emitter.on('likeDetailSuccess', (data) => {
    //   判断是否是当前的item
    if (props.data.id == data) {
        if (props.data.isLike) {
            props.data.likeNum--
            props.data.isLike = 0
            imgLoveUrl.value = isSeleteLike.noselete
        } else {
            props.data.likeNum++
            props.data.isLike = 1
            imgLoveUrl.value = isSeleteLike.selete
        }
    }
})
emitter.on('collectDetailSuccess', (data) => {
    //   判断是否是当前的item
    if (props.data.id == data) {
        if (props.data.isCollect) {
            props.data.collectNum--
            props.data.isCollect = 0
            imgCollectUrl.value = isSeleteCollect.noselete
        } else {
            props.data.collectNum++
            props.data.isCollect = 1
            imgCollectUrl.value = isSeleteCollect.selete
        }
    }
})
emitter.on('likeVideoDetailSuccess', (data) => {
    if (props.data.id == data) {
        if (props.data.isLike) {
            props.data.likeNum--
            props.data.isLike = 0
            imgLoveUrl.value = isSeleteLike.noselete
        } else {
            props.data.likeNum++
            props.data.isLike = 1
            imgLoveUrl.value = isSeleteLike.selete
        }
    }
})
emitter.on('collectVideoDetailSuccess', (data) => {
    //   判断是否是当前的item
    if (props.data.id == data) {
        if (props.data.isCollect) {
            props.data.collectNum--
            props.data.isCollect = 0
            imgCollectUrl.value = isSeleteCollect.noselete
        } else {
            props.data.collectNum++
            props.data.isCollect = 1
            imgCollectUrl.value = isSeleteCollect.selete
        }
    }
})
onUnmounted(() => {
    emitter.off('likeDetailSuccess')
    emitter.off('collectDetailSuccess')
    emitter.off('likeVideoDetailSuccess')
    emitter.off('collectVideoDetailSuccess')
})
</script>
<style lang="scss" scoped>
@import '@/assets/css/index/waterFall.scss';
</style>