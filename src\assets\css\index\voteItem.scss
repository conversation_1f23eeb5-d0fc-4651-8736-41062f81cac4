.item {
    margin-bottom: 50px;
    background: linear-gradient(308deg, #F1F8FF 0%, #E3F3FF 46%, #F3F7FF 100%);
    border-radius: 18px;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0 70px;
    font-size: var(--size_26);

    .sid {
        width: 100%;
        text-align: left;
        color: #999;
        font-size: 24px;
        padding-top: 20px;
    }

    .title {
        margin: 12px 0 50px;
        font-weight: 500;
        font-size: 34px;
        color: #000000;
        text-align: center;
        // word-break: break-all;
        // word-wrap: break-word;
        // white-space: normal;
        // /* 允许换行 */
        // overflow-wrap: break-word;
        /* 允许长单词换行 */
        overflow-wrap: break-word;
        white-space: pre-wrap; /* 或 normal */
    }

    .top {
        margin-bottom: 13px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        color: rgba(0, 0, 0, 0.7);
    }

    .pkResult {
        width: 100%;

        .pkCon {
            padding: 0 100px;
            display: flex;
            justify-content: space-between;

            img {
                width: 240px;
                height: 64px;
            }
        }

    }

    .pk {
        margin: 30px 0 42px;
        width: 100%;
        display: flex;
        justify-content: space-between;
        align-items: stretch;
        font-size: 30px;
        line-height: 44px;
        font-weight: 500;
        color: #FFFFFF;

        .square {
            flex: 1;
            display: flex;

            .squareCon {
                background: url("@/assets/images/vote/blue.webp");
                background-size: 100% 100%;
                width: 100%;
                display: flex;
                flex-direction: column;
                padding: 30px 40px;

                .name {
                    padding-right: 24px;
                    flex: 1;
                }

                .num {
                    display: flex;
                    font-size: 26px;
                    line-height: 37px;
                    color: rgba(255, 255, 255, 0.8);
                    word-break: break-all;
                    word-wrap: break-word;

                    .leftNum {
                        padding-right: 46px;
                    }
                }
            }
        }

        .pkImg {
            width: 96px;
            height: 44px;
            margin: 0 10px;
            align-self: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .negative {
            flex: 1;
            display: flex;
            text-align: right;

            .negativeCon {
                background: url("@/assets/images/vote/red.webp");
                background-size: 100% 100%;
                width: 100%;
                display: flex;
                flex-direction: column;
                padding: 30px 40px;

                .name {
                    padding-left: 24px;
                    flex: 1;
                }

                .num {
                    display: flex;
                    justify-content: flex-end;
                    font-size: 26px;
                    line-height: 37px;
                    color: rgba(255, 255, 255, 0.8);
                    word-break: break-all;
                    word-wrap: break-word;

                    .leftNum {
                        padding-right: 46px;
                    }
                }
            }
        }

        .visiblity {
            opacity: 0;
        }

    }

    .voteBtn {
        width: 80%;
        display: flex;
        justify-content: space-between;
        margin: 0 155px 36px;

        .btn {
            width: 180px;
            height: 66px;
            background: #5FA9FE;
            border-radius: 33px;
            color: #FFFFFF;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .negativeBtn {
            background: #F87D9B;
        }
    }

    .summary {
        margin-bottom: 32px;
        color: rgba(0, 0, 0, 0.5);
    }
}