.con {
    margin: 26px 66px;
    padding-bottom: env(safe-area-inset-bottom);
    padding-bottom: constant(safe-area-inset-bottom);
    padding-top: env(safe-area-inset-top);
    padding-top: constant(safe-area-inset-top);

    .top {
        font-size: var(--size_36);
        color: var(--mainTtileColor);
        line-height: 48px;
        margin-bottom: 38px;
        display: flex;

        .title {
            flex: 1;
            text-align: center;
        }

        img {
            width: 40px;
            height: 40px;
        }
    }

    .groupbox {
        height: 480px;
        overflow: auto;

        .item {
            padding: 14px 10px;

            .laberTop {
                display: flex;
                padding: 16px 0;

                .name {
                    flex: 1;
                    font-size: var(--size_22);
                    color: #333333;
                }
            }

            .detail {
                padding: 16px 20px;
                background: #F3F3F3;
                border-radius: 6px;
                color: rgba(0, 0, 0, 0.5);
                line-height: 36px;
                font-size: 24px;
            }

            :deep(.van-radio__icon) {
                height: 30px;
                width: 30px;
            }

            :deep(.van-icon) {
                height: 30px;
                width: 30px;
            }

            /* 如果你想改变选中状态下的图标大小 */
            :deep(.van-radio__icon .van-icon) {
                font-size: 25px;
                /* 设置背景大小 */
            }

            :deep(.van-checkbox__icon) {
                height: 30px;
                width: 30px;
            }

            :deep(.van-icon) {
                height: 30px;
                width: 30px;
            }

            /* 如果你想改变选中状态下的图标大小 */
            :deep(.van-checkbox__icon--checked) {
                font-size: 30px;
                /* 设置背景大小 */
            }
        }
    }


    .labelMore {
        display: flex;
        align-items: center;

        img {
            margin-left: 20px;
            width: 38px;
            height: 38px;
        }
    }

    .publish {
        display: flex;
        align-items: center;
        justify-content: center;

        .btn {
            margin-top: 9px;
            // width: 242px;
            padding: 0 50px;
            height: 68px;
            display: flex;
            align-items: center;
            text-align: center;
            color: #C4E5FF;
            font-size: var(--size_28);
            font-weight: var(--weight5);
            background: url("@/assets/images/index/publish.png") no-repeat;
            background-size: 100% 100%;
        }

    }
}