<template>
    <div>
        <div class="con">
            <div class="top">
                <img @click="back" src="@/assets/images/index/back (2).png" />
                <div class="title">{{ title }}</div>
            </div>
            <div class="groupbox">
                <van-radio-group v-model="radio" shape="round" @change="onChange">
                    <div class="item" v-for="item in dataList" :key="item.id">
                        <div class="laberTop">
                            <div class="name">{{ item.title }}</div>
                            <van-radio :name="item.id"></van-radio>
                        </div>
                        <div class="detail" v-if="radio.includes(item.id) && item.info">{{ item.info }}</div>
                    </div>
                </van-radio-group>
            </div>

            <div class="publish">
                <div class="btn" @click.stop="publish">{{ $t('report.next') }}
                </div>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, watch, onMounted, onActivated, onUnmounted, onDeactivated } from 'vue';
import emitter from '@/utils/mitt.js'
import { showToast } from 'vant';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
import { useRoute, useRouter } from 'vue-router';
const route = useRoute();
const router = useRouter()
const radio = ref('');
const dataList = ref([])
const title = ref('')
const data = ref({})
onMounted(() => {
    console.log('reportTwo页面挂载');
    // 防止外部滚动事件影响
    document.body.style.overflow = 'auto';

    // 如果是从视频页面来的，确保视频页面的滚动事件不会影响举报页面
    if (route.query.fromPath && route.query.fromPath.includes('/video')) {
        console.log('从视频页面进入举报页面，阻止事件冒泡');

        // 添加事件拦截器
        const preventPropagation = (e) => {
            e.stopPropagation();
        };

        document.querySelector('.con')?.addEventListener('wheel', preventPropagation);

        // 组件卸载时移除事件拦截器
        onUnmounted(() => {
            document.querySelector('.con')?.removeEventListener('wheel', preventPropagation);
            emitter.off('enterReport');
        });
    }
})
// 激活钩子函数
onActivated(() => {
    console.log('reportTwo页面激活');
    console.log('route', route.query);
    data.value = route.query
    dataList.value = JSON.parse(route.query.dataList).children
    console.log(JSON.parse(route.query.dataList))
    const str = t('report.reportTwoTitle');
    const newStr = str.replace('%title%', JSON.parse(route.query.dataList).title);
    title.value = newStr
})
// 失活钩子函数
onDeactivated(() => {
    console.log('reportTwo页面失活');
})
emitter.on('enterReport', () => {
    console.log('初始化')
    // 初始化
    radio.value = ''
})
const onChange = (values) => {
    console.log(values); // 这里的values是当前所有勾选的复选框的name组成的数组
};
const back = () => {
    // 否则正常返回
    router.go(-1);
    emitter.emit('homeBack');
};
// // 点击下一步
const publish = () => {
    console.log(radio.value)
    if (radio.value) {
        const newData = {
            reportId: data.value.reportId,
            reportType: data.value.reportType,
            complainType: data.value.complainType,
            menuId: dataList.value[radio.value].menuId,
            toComplainUserId: data.value.toComplainUserId,
            name: data.value.name,
            title: JSON.parse(route.query.dataList).title,
            backNum: data.value.backNum,
        }
        router.push({ path: '/reportEnd', query: newData })

    } else {
        showToast(t('toast.nextInfo'));
    }
}
// router.beforeEach((to, from, next) => {
//   if (from.path === '/reportEnd') {
//     console.log(router.currentRoute.value.query.twoData)
//     // 当从下一页回退到此页时，恢复selectedOption的值
//     radio.value = router.currentRoute.value.query.twoData || null;
//     console.log(radio.value)
//   }
//   next();
// });
onUnmounted(() => {
    emitter.off('enterReport')
})
</script>
<style></style>
<style lang="scss" scoped>
@import '@/assets/css/common/reportCheckbox.scss';
</style>