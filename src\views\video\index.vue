<template>
    <div class="home">
        <swiper direction="vertical" :allowTouchMove="!isShowDetail" :touchRatio="1" :touchAngle="45" @swiper="onSwiper"
            @slideChange="onSlideChange" class="video-swiper" :slidesPerView="1" :slidesPerGroup="1" :initialSlide="0"
            :observer="true" :observeParents="true">
            <swiper-slide v-for="(item, index) in list" :key="item.id">
                <videoPlay :data="item" @commentStateChange="handleCommentStateChange"
                    @videoStateChange="(isPlaying) => handleVideoStateChange(index, isPlaying)" @delete="handleDelete"
                    @back="back">
                </videoPlay>
            </swiper-slide>
        </swiper>


    </div>
    <div v-if="isShowDetail">
        <Detail :data="data" :tooltipType="tooltipType" type="video" :isShowDetail="isShowDetail"
            @closePopup="closePopup">
        </Detail>
    </div>
</template>

<script setup>
import videoPlay from "@/components/index/videoPlay.vue";
import { Swiper, SwiperSlide } from 'swiper/vue';
import 'swiper/css';
import { ref, reactive, onMounted, onUnmounted, watch, nextTick, defineAsyncComponent, onActivated, onDeactivated } from "vue";
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import emitter from '@/utils/mitt.js'
const Detail = defineAsyncComponent(() =>
    import('@/components/detail/index.vue')
);
defineOptions({ name: 'videoPage' })
import { useRouter, useRoute } from 'vue-router';
const router = useRouter()
const route = useRoute();
import { Icon } from 'vant';
import { showToast } from 'vant';
import 'vant/lib/index.css';
import { getDynamicList, getDynamicDetail } from "@/api/home.js"
// 视频数据
const list = ref([])
const pageSize = ref(5); // 每页加载的视频数量
const pageNum = ref(1);
const total = ref(0);
const isLoading = ref(false);
const hasMore = ref(true);
const bufferSize = 3; // 虚拟列表缓冲区大小
const data = ref({});
const isShowDetail = ref(false)
const tooltipType = ref('')
const dynamicsId = ref(0)
const dynamicsType = ref(0)
const userId = ref(0)
// 获取视频数据
const fetchVideos = async () => {
    if (isLoading.value || !hasMore.value) return;
    console.log('fetchVideos', pageNum.value);
    isLoading.value = true;
    try {
        const indexUserId = countStore.seeUserRoleId ? countStore.seeUserRoleId : countStore.loginData.roleId
        const query = {
            dynamicsType: dynamicsType.value,
            pageNum: pageNum.value,
            pageSize: pageSize.value,
            userId: dynamicsType.value == '1' ? userId.value : indexUserId,
            dynamicsTypeSubType: 3,
            dynamicsTypeParentType: 2,
            currentId: pageNum.value == 1 ? dynamicsId.value : ''
        }
        console.log('query', query);
        const res = await getDynamicList(query)
        if (res.code == 200) {
            console.log(res)
            total.value = res.total
            if (pageNum.value == 1) {
                list.value = res.rows
            } else {
                res.rows.forEach(item => {
                    list.value.push(item)
                });
            }
        }
        if (list.value.length < total.value) {
            hasMore.value = true
        } else {
            hasMore.value = false
        }
        isLoading.value = false;
    } catch (error) {
        console.error('获取视频数据失败:', error);
    } finally {
        isLoading.value = false;
    }
};

// 处理滑动到底部
const handleScroll = () => {
    // console.log('处理滑动到底部')
    if (!swiperRef.value || isLoading.value || !hasMore.value) return;
    const { progress, activeIndex } = swiperRef.value;
    // 当滑动到倒数第 bufferSize 个视频时加载更多
    if (activeIndex >= list.value.length - bufferSize) {
        console.log('加载更多');
        pageNum.value++
        fetchVideos();
    }
};

// 是否允许滚动
const isScrollAllowed = ref(true);

// 处理评论面板状态变化
const handleCommentStateChange = (datas, type, isShow, focusInput = false) => {
    data.value = datas
    tooltipType.value = type
    isShowDetail.value = isShow
    if (focusInput && isShow) {
        setTimeout(() => {
            emitter.emit('focusDetailInput')
        }, 300)
    }
};
const closePopup = () => {
    isShowDetail.value = false;
    // 通知所有 videoPlay 组件评论已关闭
    emitter.emit('closeComment');
    
    // 延迟一点时间再更新 swiper，确保 DOM 已更新
    setTimeout(() => {
        if (swiperRef.value) {
            swiperRef.value.update();
        }
    }, 100);
}

const swiperRef = ref(null);
const currentVideoIndex = ref(0);
const lastFetchedVideoId = ref(null);
const lastScrollTime = ref(0);
const lastScrollDirection = ref(null);
const SCROLL_THRESHOLD = 300;

// 获取视频详情
const getVideoDetails = async (videoId) => {
    // 如果已经获取过该视频的详情，则不再重复获取
    if (lastFetchedVideoId.value === videoId) {
        return
    }
    try {
        const res = await getDynamicDetail(videoId)
        console.log('获取视频详情:', videoId)
        lastFetchedVideoId.value = videoId
        if (res.code === 200) {

        } else if (res.code == 500) {
            //   showToast(t('toast.dynamicFail'));
            console.log('动态已删除')
            //    动态已删除
            handleDelete(videoId)
        }
    } catch (error) {
        console.error('获取视频详情失败:', error)
    }
}

// 处理视频播放状态变化
const handleVideoStateChange = (index, isPlaying) => {
    if (isPlaying) {
        // currentVideoIndex.value = index
        // // 获取当前播放视频的详情
        // getVideoDetails(list.value[index].id)
    }
}


const onSwiper = (swiper) => {
    swiperRef.value = swiper; // 保存到全局以便 videoPlay 组件访问
    window.swiper = swiper;
    // 添加进度监听
    swiper.on('progress', handleScroll);
    // 获取第一个视频的详情
    if (list.value.length > 0) {
        getVideoDetails(list.value[0].id);
    }
};

// 添加一个变量来存储待删除的视频ID
const pendingDeleteVideoId = ref(null);
// 处理删除视频
const handleDelete = (videoId) => {
    // 如果只有一个视频，不执行删除
    if (list.value.length === 1) {
        // showToast('无法删除最后一个视频');
        return;
    }

    // 存储待删除的视频ID
    pendingDeleteVideoId.value = videoId;
    // showToast('请滑动到其他视频后自动删除');
};


// ... existing code ...
// 修改滑动切换事件处理函数
const onSlideChange = () => {
    console.log('slide change');
    // 如果有待删除的视频，且用户已经滑动到其他视频，则执行删除
    if (pendingDeleteVideoId.value && swiperRef.value) {
        const currentIndex = swiperRef.value.activeIndex;
        const indexToDelete = list.value.findIndex(item => item.id === pendingDeleteVideoId.value);
        // 确保当前不在要删除的视频上
        if (indexToDelete !== currentIndex) {
            // 记录当前活动页面的索引
            const activeIndex = swiperRef.value.activeIndex;
            // 计算新的索引
            let newIndex;
            if (indexToDelete < activeIndex) {
                // 如果删除的视频在当前视频之前，保持当前索引减1
                newIndex = activeIndex - 1;
            } else if (indexToDelete > activeIndex) {
                // 如果删除的视频在当前视频之后，保持当前索引不变
                newIndex = activeIndex;
            }
            // 确保索引在有效范围内
            newIndex = Math.max(0, Math.min(newIndex, list.value.length - 1));
            // 更新当前视频索引
            currentVideoIndex.value = newIndex;
            // 更新索引
            swiperRef.value.slideTo(newIndex, 0);
            // 执行删除操作
            list.value.splice(indexToDelete, 1);
            pendingDeleteVideoId.value = null;
            swiperRef.value.update(); // 更新Swiper实例以反映变
            // showToast('删除成功');
        }
    }

    // 获取当前滑动到的视频详情
    if (swiperRef.value) {
        const currentIndex = swiperRef.value.activeIndex;
        getVideoDetails(list.value[currentIndex].id);
    }
};
// 返回首页
const back = (() => {
    console.log('返回首页')
    // 检查是否是从index页面进入的
    if (route.query.fromIndex) {
        // 如果是从index页面进入的，返回时触发homeBack事件
        router.go(-1);
        // 延迟一点时间再触发homeBack事件，确保组件已经激活
        setTimeout(() => {
            emitter.emit('homeBack');
        }, 100);
    } else {
        // 否则正常返回
        router.go(-1);
    }
})

// 记录滚动事件监听状态
const scrollListenersActive = ref(true);

// 处理鼠标滚轮事件
const handleWheel = (event) => {
    // console.log('滚动')
    if (isShowDetail.value || !swiperRef.value) return;

    event.preventDefault();
    const currentTime = Date.now();
    const currentDirection = event.deltaY > 0 ? 'down' : 'up';

    if (currentTime - lastScrollTime.value < SCROLL_THRESHOLD &&
        currentDirection === lastScrollDirection.value) {
        return;
    }

    lastScrollTime.value = currentTime;
    lastScrollDirection.value = currentDirection;

    if (event.deltaY > 0) {
        swiperRef.value.slideNext();
    } else if (event.deltaY < 0) {
        swiperRef.value.slidePrev();
    }
};

// 移除所有滚动相关的事件监听
const removeScrollListeners = () => {
    console.log('移除视频页面滚动监听');
    window.removeEventListener('wheel', handleWheel);
    if (swiperRef.value) {
        swiperRef.value.off('progress', handleScroll);
    }
    scrollListenersActive.value = false;
};

// 添加所有滚动相关的事件监听
const addScrollListeners = () => {
    console.log('添加视频页面滚动监听');
    window.addEventListener('wheel', handleWheel, { passive: false });
    if (swiperRef.value) {
        swiperRef.value.on('progress', handleScroll);
    }
    scrollListenersActive.value = true;
};

// 组件激活时
onActivated(() => {
    console.log('视频页面激活');
    // 检查是从哪个页面返回的
    const fromPath = router.currentRoute.value.query.fromPath;
    const originalPath = router.currentRoute.value.query.originalPath;

    console.log('视频页面激活 - fromPath:', fromPath, 'originalPath:', originalPath);

    // 如果是从举报页面或home页面返回，不重新添加滚动监听
    if (fromPath && (fromPath.includes('report') || fromPath === 'reportComplete')) {
        console.log('从举报页面返回，不重新添加滚动监听');
        // 如果是从举报完成页面返回，可能需要刷新数据
        if (fromPath === 'reportComplete') {
            console.log('举报完成返回，刷新视频数据');
            // 可以选择是否刷新数据

            // 清除举报相关的路径参数，但保留必要的视频参数
            if (router.currentRoute.value.query.id && router.currentRoute.value.query.type) {
                const query = {
                    id: router.currentRoute.value.query.id,
                    type: router.currentRoute.value.query.type
                };

                // 使用replace而不是push，避免在历史记录中创建新条目
                router.replace({
                    path: '/video',
                    query: query
                });
            }
        }
    } else if (fromPath && fromPath.includes('/home')) {
        // 从home页面返回，重新添加滚动监听
        console.log('从home页面返回，重新添加滚动监听');
        addScrollListeners();
    } else {
        // 否则重新添加滚动监听
        addScrollListeners();
    }
});

// 组件失活时
onDeactivated(() => {
    console.log('视频页面失活');
    const toPath = router.currentRoute.value.fullPath;

    console.log('视频页面失活 - 目标路径:', toPath);

    // 如果是进入举报页面或home页面，移除滚动监听
    if (toPath.includes('report') || toPath.includes('/home')) {
        removeScrollListeners();
        console.log('进入举报页面或home页面，移除滚动监听');
    }
});

onMounted(() => {
    console.log('视频页面挂载');
    console.log('route', route.query);
    dynamicsId.value = route.query.id
    dynamicsType.value = route.query.type
    userId.value = route.query.userId
    console.log('userId', userId.value);
    fetchVideos();

    // 添加滚动监听
    addScrollListeners();
});

onUnmounted(() => {
    console.log('视频页面卸载');
    removeScrollListeners();
    window.swiper = null;

});

// 监听评论面板状态变化
watch(isShowDetail, (newValue) => {
    if (swiperRef.value) {
        // 如果评论面板打开，禁止滚动
        // 如果评论面板关闭，允许滚动
        swiperRef.value.allowTouchMove = !newValue;
    }
});

</script>

<style scoped lang="scss">
.home {
    height: 100vh;
    overflow: hidden;
    display: flex;
}

.video-swiper {
    flex: 1;
    height: 100vh;
    transition: width 0.3s ease;

    :deep(.swiper-slide) {
        height: 100vh;
        width: 100%;
    }
}

.action-buttons {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 100;

    .action-button {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(0, 0, 0, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 10px;

        img {
            width: 30px;
            height: 30px;
        }
    }
}

.comment-panel {
    width: 0;
    height: 100%;
    background: #1c1c1c;
    transition: width 0.3s ease;
    overflow: hidden;
    display: flex;
    flex-direction: column;

    &.show-comment {
        width: 400px;
    }

    .comment-header {
        padding: 16px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);

        .title {
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: white;
            font-size: 16px;

            .close-icon {
                cursor: pointer;
                font-size: 20px;
                color: #fff;
            }
        }
    }

    .comment-list {
        flex: 1;
        overflow-y: auto;
        padding: 16px;
        -webkit-overflow-scrolling: touch;
        overscroll-behavior: contain;

        &::-webkit-scrollbar {
            width: 4px;
        }

        &::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
        }

        &::-webkit-scrollbar-track {
            background: transparent;
        }

        .comment-item {
            display: flex;
            margin-bottom: 20px;

            .user-avatar {
                width: 40px;
                height: 40px;
                margin-right: 12px;

                img {
                    width: 100%;
                    height: 100%;
                    border-radius: 50%;
                    object-fit: cover;
                }
            }

            .comment-content {
                flex: 1;
                color: white;

                .user-name {
                    font-size: 14px;
                    font-weight: 500;
                    margin-bottom: 4px;
                }

                .comment-text {
                    font-size: 14px;
                    margin-bottom: 8px;
                }

                .comment-info {
                    font-size: 12px;
                    color: rgba(255, 255, 255, 0.6);
                    margin-bottom: 8px;

                    .time {
                        margin-right: 12px;
                    }

                    .translate {
                        cursor: pointer;
                    }
                }

                .comment-actions {
                    display: flex;
                    justify-content: space-between;
                    align-items: center;

                    .like {
                        display: flex;
                        align-items: center;
                        gap: 4px;
                        color: rgba(255, 255, 255, 0.6);
                        font-size: 12px;
                    }

                    .van-icon {
                        font-size: 20px;
                        color: rgba(255, 255, 255, 0.6);
                    }
                }
            }
        }
    }

    .comment-input {
        padding: 16px;
        border-top: 1px solid rgba(255, 255, 255, 0.1);
        display: flex;
        align-items: center;
        gap: 12px;

        input {
            flex: 1;
            background: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            color: white;
            outline: none;

            &::placeholder {
                color: rgba(255, 255, 255, 0.6);
            }
        }

        .input-actions {
            display: flex;
            gap: 12px;
            color: rgba(255, 255, 255, 0.6);

            .van-icon {
                font-size: 20px;
                color: rgba(255, 255, 255, 0.6);
                cursor: pointer;
            }
        }
    }
}
</style>