<template>
    <div>
        <van-popup v-model:show="dialogVisible" title="" show-cancel-button :close-on-click-overlay="false"
            @click.stop="onClickPopup" @click-overlay.stop="onClickOverlay">
            <div class="deleteCon">
                <div class="title">{{ title }}</div>
                <div class="like" v-if="data.length">
                    <!-- <img src="@/assets/images/index/likeList.png"></img> -->
                    <div class="likeList">
                        <template v-for="(item, index) in data" :key="index">
                            <span class="like-item">
                                <img src="@/assets/images/index/likeList.png" />
                                <span class="item-text" dir="auto">{{ item }}</span>
                            </span>
                            <span v-if="index !== data.length - 1" class="separator">、</span>
                        </template>
                    </div>
                </div>
                <div class="close" @click="close()">
                    <img src="@/assets/images/detail/close.png" />
                </div>
            </div>
        </van-popup>
    </div>
</template>
<script setup>
import { ref, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const dialogVisible = ref(false);
const props = defineProps({
    dialogLikeList: {
        type: Boolean,
        require: false,
        default: false
    },
    data: {
        type: Array,
        require: false,
        default: []
    }
})
watch(() => props.dialogLikeList, (val) => {
    // console.log(val)
    dialogVisible.value = val
}, { immediate: true })
const title = ref('')
onMounted(() => {
    const str = t('detail.likeListTitle');
    const newStr = str.replace('%num%', props.data.length);
    title.value = newStr
})
const emits = defineEmits(['dialogClose'])
// 取消
const close = (index) => {
    emits('dialogClose')
}
const onClickPopup = (() => {

})
const onClickOverlay = (() => {

})
</script>
<style lang="scss" scoped>
.van-popup--center {
    max-width: 85%;
    width: 80%;
    border-radius: 8px;
}

:deep(.van-overlay) {
    background: rgba(0, 0, 0, 0.4);
}

.deleteCon {
    padding: 26px 31px;
    position: relative;

    .title {
        font-weight: 500;
        font-size: 28px;
        color: #333333;
        line-height: 40px;
        margin-bottom: 10px;

    }

    .like {
        display: flex;
        height: 200px;
        font-size: 24px;
        color: #666666;
        overflow: auto;

        img {
            width: 32px;
            height: 32px;
            // margin-left: 6px;
        }

        .likeList {
            line-height: 35px;

            span,
            img {
                vertical-align: middle;
            }
             .like-item {
                display: inline-flex;
                align-items: center;
                direction: ltr;
            }
            
            .item-text {
                unicode-bidi: isolate;
            }
            
            .separator {
                display: inline-block;
                margin: 0 2px;
            }
        }

    }

    .close {
        position: absolute;
        top: 18px;
        right: 18px;

        img {
            width: 52px;
            height: 52px;
        }
    }
}
</style>