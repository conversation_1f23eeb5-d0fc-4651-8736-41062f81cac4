<template>
    <van-popup :z-index="2000" v-model:show="dialogVisible" :showConfirmButton="false" :close-on-click-overlay="false">
        <div class="con">
            <div class="top">
                <div class="title">{{ $t('add.aitUser') }}</div>
                <img @click="close" src="@/assets/images/index/close.png" />
            </div>
            <van-search v-model="input" clearable :placeholder="$t('add.searchPlaceholder')" @search="search"
                @clear="clear" show-action>
                <template #action>
                    <img class="searchImg" @click="search" src="@/assets/images/index/search.png" />
                </template>
            </van-search>
            <div class="groupbox">
                <van-list @load="changeData" :immediate-check="true" :offset="1">
                    <van-checkbox-group v-model="checked" shape="round" max="5"
                        @change="checkedResultChange($event, item)">
                        <div v-if="mockData.length">
                            <div class="item" v-for="item in mockData" :key="item.friendId">
                                <avatar :url="item.icon" className="comment" type="msg"></avatar>
                                <div class="name">{{ item.name }}</div>
                                <van-checkbox :name="item.friendId" @click="checkedChange(item)"></van-checkbox>
                            </div>
                        </div>
                        <div v-else class="noComment">
                            <Empty :title="$t('empty.content')" />
                        </div>
                    </van-checkbox-group>
                </van-list>
            </div>

            <div class="publish">
                <div class="prompt">{{ $t('toast.aitInfo') }}</div>
                <div class="btn" @click="publish">{{ $t('add.confirm') }}</div>
            </div>
        </div>
    </van-popup>

</template>
<script setup>
import { ref, watch, onMounted, onActivated, defineAsyncComponent } from 'vue';
import emitter from '@/utils/mitt.js'
import { friendShip } from "@/api/home.js"
import Empty from "@/components/common/empty.vue"
const avatar = defineAsyncComponent(() =>
    import('@/components/common/avatar.vue')
);
const dialogVisible = ref(false);
const input = ref('')
const maxNum = ref(5)
const props = defineProps({
    dialogFormAit: {
        type: Boolean,
        require: false,
        default: false
    },
})
const pageNum = ref(1)
const pageSize = ref(10)
const total = ref(0)
const checked = ref([]);
const mockData = ref([])
const aitList = ref([])
const checkoutData = ref([])
watch(() => props.dialogFormAit, (val) => {
    // console.log(val)
    dialogVisible.value = val
}, { immediate: true })
watch(input, (val) => {
    console.log('输入内容改变:', val);
    // 在这里处理输入内容改变的逻辑
    if (val === '') {
        console.log('输入内容为空');
        clear()
    }
});
// 激活钩子函数
onActivated(() => {
    // 在这里执行你的代码

})
onMounted(() => {
    friendList()
})
const checkedChange = (val) => {
    if (checked.value.includes(val.friendId)) {
        aitList.value.push(val.name)
        checkoutData.value.push(val)
    } else {
        // 删除
        checkoutData.value.forEach((item, index) => {
            if (item.friendId == val.friendId) {
                checkoutData.value.splice(index, 1);
            }
        });
        aitList.value = []
        checkoutData.value.forEach((item) => {
            aitList.value.push(item.name)
        })
    }

}
const checkedResultChange = (val) => {
    // console.log(aitList.value)
    // console.log(checked.value)

}
emitter.on('seleteAit', (data) => {
    console.log(data);
    if (data) {
        checked.value = [...data.toUserIdList];
        aitList.value = [...data.aitList]
        checkoutData.value=[...data.aitData]
    }
});
const changeData = () => {
    console.log('更新数据')
    if (mockData.value.length < total.value) {
        pageNum.value++
        friendList()
        console.log(pageNum.value)
        console.log('更新数据')
    }
}
// 搜索
const search = () => {
    pageNum.value = 1
    friendList()
}
// 清除
const clear = () => {
    pageNum.value = 1
    friendList()
}
const friendList = () => {
    const query = {
        searchValue: input.value,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
    }
    friendShip(query)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                total.value = res.total
                if (pageNum.value == 1) {
                    mockData.value = res.rows
                } else {
                    res.rows.forEach(item => {
                        mockData.value.push(item)
                    });
                }
            }

        })
        .catch(function (error) {
            console.log(error);

        });
}
// 定义组件的事件
const emits = defineEmits(['dialogCloseAit'])
const close = () => {
    emits('dialogCloseAit', false)
}

const publish = () => {
    console.log('publish number:', checked.value)
    emits('dialogCloseAit', false)
    const data = {
        aitList: aitList.value,
        toUserIdList: checked.value,
        checkoutData: checkoutData.value,
    }
    emitter.emit('aitSeleteConfirm', data)
    input.value = ''
    checked.value = []
}
</script>
<style lang="scss" scoped>
@import '@/assets/css/common/checkbox.scss';

.noComment {
    height: 300px;
}

:deep(.van-checkbox__icon) {
    height: 30px;
    width: 30px;
}

:deep(.van-icon) {
    height: 30px;
    width: 30px;
}

/* 如果你想改变选中状态下的图标大小 */
:deep(.van-checkbox__icon--checked) {
    font-size: 30px;
    /* 设置背景大小 */
}
 

:deep(.van-search) {
    padding: 0;
}

:deep(.van-search__field) {
    height: 56px;
    // padding: 30px 0;
    background: #F3F3F3;
}

:deep(.van-search__content) {
    background: #F3F3F3;
}

:deep(.van-field__left-icon) {
    display: none;
}


:deep(.van-search__action) {
    display: flex;
    align-items: center;
}

.publish {
    .prompt {
        position: absolute;
        bottom: 20px;
        left: 20px;
        color: var(--bearTtileColor);
        font-size: 20px;
    }
}
</style>