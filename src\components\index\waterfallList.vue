<template>
    <User v-if="isShowUser" :data="countStore.userData"></User>
    <Tab v-if="tabs.length > 0 && !isShowUser" :data="tabs" :popoverData="popoverDatas" @changeTab="changeTab"
        @changePopover="changePopover"></Tab>
    <div v-if="loading">
        <skeleton />
    </div>
    <div v-else :class="isMatchType ? 'matchTypeCon' : 'waterFallCon'">
        <van-list class="vanList" ref="waterfallList" @load="changeWaterfallData" :offset="1">
            <div v-if="list.length">
                <div v-masonry="getMasonryId()" transition-duration="300" item-selector=".item" >
                    <div v-masonry-tile class="item" v-for="(item, index) in list" :key="index">
                        <Waterfall :dynamicType="countStore.loginRole" :type='type' :data="item" :index="index"
                            class="water-fall" @showDetail="showDetail" />
                    </div>
                </div>
            </div>
            <div v-else class="noComment">
                <Empty :title="$t('empty.content')" />
            </div>
        </van-list>
    </div>
    <div v-if="countStore.loginRole == 'oneself' && !isMatchType && (activeTab == 1 || activeTab == 4 || activeTab == 5)"
        class="affix" @click="addClick">
        <img src="@/assets/images/index/add.webp" />
    </div>
    <div v-if="countStore.loginRole == 'oneself' && !isMatchType && activeTab == 5" class="match" @click="matchClick">
        <img src="@/assets/images/match/game-icon.webp" />
    </div>
    <div v-if="dialogFormAdd">
        <Add :dialogFormAdd="dialogFormAdd" :data="addData" :type="uploadType" @dialogCloseAdd="dialogCloseAdd"
            @addSuccess="addSuccess">
        </Add>
    </div>
    <div v-if="isShowDetail">
        <Detail :data="data" :tooltipType="tooltipType" :isShowDetail="isShowDetail" :index="index"
            @closePopup="closePopup"></Detail>
    </div>
</template>
<script setup>
import { ref, onMounted, onBeforeMount, onActivated, onUnmounted, watch, nextTick, inject, defineAsyncComponent, onDeactivated } from 'vue';
import Waterfall from "@/components/index/waterFall.vue"
import { showToast } from 'vant';
const User = defineAsyncComponent(() =>
    import('@/components/index/user.vue')
);
const skeleton = defineAsyncComponent(() =>
    import('@/components/common/skeleton.vue')
)
const Add = defineAsyncComponent(() =>
    import('@/components/dialog/add.vue')
);
const Empty = defineAsyncComponent(() =>
    import('@/components/common/empty.vue')
);
const Detail = defineAsyncComponent(() =>
    import('@/components/detail/index.vue')
);
const Tab = defineAsyncComponent(() =>
    import('@/components/index/tab.vue')
);
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import { useRouter, useRoute } from 'vue-router';
const router = useRouter()
const route = useRoute()
import emitter from '@/utils/mitt.js'
//页面数据请求
import axios from 'axios';
import { getDynamicList, getDynamicDetail, axiosCancel } from "@/api/home.js"
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import _ from 'lodash';
const dialogFormAdd = ref(false)
const addData = ref({})
const isShowUser = ref(false)
const pageNum = ref(1)
const pageSize = ref(10)
const list = ref([]);
const total = ref(0)
const loading = ref(true)
const type = ref('dynamic')
const $redrawVueMasonry = inject('redrawVueMasonry')
const waterfallList = ref(null);
const isRefurbish = ref(false)
const data = ref({})
const isShowDetail = ref(false)
const tooltipType = ref('')
const index = ref()
const tabs = ref([]);
const popoverDatas = ref([]);
const tabType = ref(1)
const newTabType = ref(1)
const uploadType = ref('')
const scrollPosition = ref(0); // 保存滚动位置
const throttledSaveScroll = ref(null); // 存储节流函数引用
const loadedImagesCount = ref(0); // 跟踪已加载的图片数量
const totalImagesInView = ref(0); // 需要加载的图片总数
const props = defineProps({
    activeTab: {
        type: Number,
        require: false,
        default: 0
    },
    isMatchType: {
        type: Boolean,
        require: false,
        default: false
    }
})
const matchClick = () => {
    router.push('/match')
}
// 获取masonry容器ID
const getMasonryId = () => {
    return props.isMatchType ? 'match-waterfall' : 'normal-waterfall';
};
// 获取正确的滚动容器
const getScrollContainer = () => {
    if (!waterfallList.value) return null;

    const rootEl = waterfallList.value.$el;

    // 尝试多种选择器找到正确的滚动容器
    let scrollContainer = rootEl.querySelector('.vanList');

    // 如果找不到直接的.vanList，尝试其他可能的容器
    if (!scrollContainer) {
        scrollContainer = rootEl.querySelector('.waterFallCon');
    }

    // 如果仍然找不到，尝试直接使用根元素
    if (!scrollContainer) {
        scrollContainer = rootEl;
    }

    return scrollContainer;
};

// 保存当前滚动位置
const saveScrollPosition = () => {
    const scrollContainer = getScrollContainer();
    if (scrollContainer) {
        const currentScrollTop = scrollContainer.scrollTop;
        scrollPosition.value = currentScrollTop;
        // console.log('保存滚动位置:', scrollPosition.value);

        // 只有当滚动位置大于0时才保存到localStorage
        if (scrollPosition.value >= 0) {
            try {
                // 根据isMatchType选择不同的键名
                const storageKey = props.isMatchType ? 'matchScrollPosition' : 'waterfallScrollPosition';
                localStorage.setItem(storageKey, scrollPosition.value);
            } catch (e) {
                console.error('保存滚动位置到localStorage失败:', e);
            }
        }
    } else {
        console.error('未找到滚动容器');
    }
};

const showDetail = (datas, type, isShow, indexs) => {
    // console.log(datas,type ,isShow, index)
    // 判断是否是视频
    if (datas.videoFrame) {
        getVideoDetails(datas.id)
    } else {
        data.value = datas
        tooltipType.value = type
        isShowDetail.value = isShow
        index.value = indexs
    }

}
// 获取视频详情
const getVideoDetails = async (videoId) => {
    try {
        const res = await getDynamicDetail(videoId)
        if (res.code === 200) {
            console.log(res)
            const data = {
                id: videoId,
                type: props.activeTab,
                fromIndex: true,// 标记是从index页面进入的
                userId: res.data.userId
            }
            router.push({ path: '/video', query: data })
        } else if (res.code == 500) {
            showToast(t('toast.dynamicFail'));
            //刷新列表
            pageNum.value = 1
            loading.value = true
            list.value = []
            getList(props.activeTab)
        }
    } catch (error) {
        console.error('获取视频详情失败:', error)
    }
}
const closePopup = () => {
    isShowDetail.value = false
}
// 更新数据
const changeWaterfallData = _.debounce((params) => {
    console.log('ss')
    if (list.value.length < total.value) {
        pageNum.value++
        getList(props.activeTab)
        console.log('更新数据list')
    }
}, 1000, { leading: true, trailing: false })
// 打开add
const addClick = () => {
    uploadType.value = ''
    console.log('sss')
    dialogFormAdd.value = true
}
// 关闭add
const dialogCloseAdd = (data) => {
    dialogFormAdd.value = data
    addData.value = {}
}// add添加或修改成功
const addSuccess = () => {
    // 先判断目前是否是动态 然后更新列表
    if (props.activeTab == 1 || props.activeTab == 4 || props.activeTab == 5) {
        loading.value = true
        list.value = []
        pageNum.value = 1
        getList(props.activeTab)
    }
}
const getList = (type) => {
    // console.log(pageNum.value)
    if (pageNum.value == 1) {
        axiosCancel()
    }
    const userId = countStore.seeUserRoleId ? countStore.seeUserRoleId : countStore.loginData.roleId
    const query = {
        dynamicsType: type,
        pageNum: pageNum.value,
        pageSize: pageSize.value,
        userId: userId,
        limitId: list.value.length ? list.value[0].id : 0,
        dynamicsTypeSubType: tabType.value,
        dynamicsTypeParentType: newTabType.value
    }

    // 保存当前的滚动位置和列表高度
    let oldScrollTop = 0;
    let oldHeight = 0;
    if (pageNum.value > 1 && waterfallList.value) {
        const rootEl = waterfallList.value.$el;
        oldScrollTop = rootEl.scrollTop;
        oldHeight = rootEl.scrollHeight;
    }

    getDynamicList(query)
        .then((res) => {
            console.log(res)
            console.log(list.value)
            if (res.code === 200) {
                total.value = res.total
                if (res.rows) {
                    if (pageNum.value == 1) {
                        list.value = res.rows
                        setTimeout(() => {
                            reattachScrollListener();
                            // 首次加载后重绘瀑布流
                            nextTick(() => {
                                const masonryId = getMasonryId();
                                $redrawVueMasonry(masonryId);
                                console.log(`首次加载完成，${masonryId}瀑布流重绘`);
                            });
                        }, 500);
                    } else {
                        // 保存旧列表长度
                        const oldLength = list.value.length;
                        
                        // 添加新内容
                        res.rows.forEach(item => {
                            list.value.push(item)
                        });
                        
                        // 在添加新内容后重绘瀑布流
                        nextTick(() => {
                            // 延迟一点时间确保DOM已更新
                            setTimeout(() => {
                                const masonryId = getMasonryId();
                                $redrawVueMasonry(masonryId);
                                console.log(`加载更多内容完成，${masonryId}瀑布流重绘`);
                            }, 100);
                        });
                    }
                }
                // console.log(list.value)
                loading.value = false
            }
        })
        .catch(function (error) {
            if (axios.isCancel(error)) {
                console.log('Request canceled', error.message);
            } else {
                console.log(error);
            }
        });
};
onMounted(() => {
    console.log(props.activeTab)
    console.log(countStore.loginRole)
    if (props.isMatchType) {
         type.value = 'hot'
        loading.value = true
        list.value = []
        getList(props.activeTab)
    }
    if (countStore.deviceType == 'Web') {
    } else {
        window.location.href = "uniwebview://enterDynamic";
    }

    // 延迟添加滚动事件监听，确保DOM已完全渲染
    setTimeout(() => {
        reattachScrollListener();
    }, 500);
})
watch(() => props.activeTab, (n, o) => {
    console.log(n)
    loading.value = true
    pageNum.value = 1
    list.value = []
    if (n == 4) {
        // 朋友圈
        type.value = 'friend'
        isShowUser.value = false
        tabType.value = 1
        newTabType.value = 1
        emitter.emit('menuChange')
        getList(props.activeTab)
    } else if (n == 1) {
        // 动态
        type.value = 'dynamic'
        isShowUser.value = true
        tabType.value = 0
        getList(props.activeTab)
    } else if (n == 2) {
        // 收藏
        type.value = 'collect'
        isShowUser.value = true
        tabType.value = 0
        getList(props.activeTab)
    } else if (n == 3) {
        // 赞过
        type.value = 'like'
        isShowUser.value = true
        tabType.value = 0
        getList(props.activeTab)
    } else if (n == 5) {
        type.value = 'hot'
        isShowUser.value = false
        tabType.value = 1
        newTabType.value = 1
        emitter.emit('menuChange')
        getList(props.activeTab)
    }
})
// 消息栏
const changeTab = (tab) => {
    console.log(tab)
    newTabType.value = tab
    loading.value = true
    pageNum.value = 1
    list.value = []
    getList(props.activeTab)
}
// 消息栏popover
const changePopover = (tab) => {
    console.log(tab)
    tabType.value = tab
    loading.value = true
    pageNum.value = 1
    list.value = []
    getList(props.activeTab)
}
// 监听到tooltip取消收藏 
emitter.on('cancelCollects', () => {
    pageNum.value = 1
    loading.value = true
    list.value = []
    getList(2)
})
// 监听到 在列表外层点赞成功 判断是否是赞过menu 如果是刷新列表
emitter.on('likeListSuccess', () => {
    // 先判断目前是否是赞过 然后更新列表
    console.log('111')
    if (props.activeTab == 3) {
        loading.value = true
        pageNum.value = 1
        list.value = []
        getList(3)
    }
})
// 监听到 在列表外层收藏成功 判断是否是收藏menu 如果是刷新列表
emitter.on('collectSuccess', () => {
    // 先判断目前是否是赞过 然后更新列表
    if (props.activeTab == 2) {
        loading.value = true
        pageNum.value = 1
        list.value = []
        getList(2)
    }
})
// 监听到更新列表
emitter.on('updateWaterfallList', () => {
    pageNum.value = 1
    loading.value = true
    list.value = []
    getList(props.activeTab)
})
// 添加图片加载完成事件监听
emitter.on('imageLoaded', (imageId) => {
    loadedImagesCount.value++;
    console.log(`图片加载完成: ${loadedImagesCount.value}/${totalImagesInView.value}`);
    
    // 当所有可见图片都加载完成时，重绘瀑布流
    if (loadedImagesCount.value >= totalImagesInView.value && totalImagesInView.value > 0) {
        console.log('所有图片加载完成，重绘瀑布流');
        const masonryId = getMasonryId();
        $redrawVueMasonry(masonryId);
    }
})
// 监听到打开add
emitter.on('openAdd', (data) => {
    uploadType.value = 'base'
    dialogFormAdd.value = true
})
// 监听到获取到修改语言
emitter.on('getTabData', () => {
    tabs.value = [{ id: 1, title: t('index.menuItems.Dynamic') }, { id: 2, title: t('video.videoTitle') }]
    popoverDatas.value = [{ id: 1, text: t('index.menuItems.New') }, { id: 2, text: t('index.menuItems.Hot') }]
})
// 监听到登录成功
emitter.on('loginSuccess', () => {
    console.log('登陆成功')
    if (countStore.loginRole == 'other') {
        type.value = 'dynamic'
        isShowUser.value = true
        getList(1); //直接调用请求方法
    } else {
        type.value = 'hot'
        isShowUser.value = false
        getList(5); //直接调用请求方法
    }
})
// 激活钩子函数
onActivated(() => {
    console.log('激活');
    console.log(isRefurbish.value);

    // 检查是否是从视频页面返回
    const referrer = document.referrer;
    if (referrer && referrer.includes('/video')) {
        console.log('从视频页面返回');
        isRefurbish.value = true;
    }

    // 根据isMatchType选择不同的键名
    const storageKey = props.isMatchType ? 'matchScrollPosition' : 'waterfallScrollPosition';
    const masonryId = getMasonryId();

    // 检查localStorage中是否有保存的滚动位置
    try {
        const savedPosition = localStorage.getItem(storageKey);
        if (savedPosition && !isNaN(Number(savedPosition))) {
            scrollPosition.value = Number(savedPosition);
            console.log(`从localStorage获取滚动位置(${storageKey}):`, scrollPosition.value);
        }
    } catch (e) {
        console.error(`从localStorage获取滚动位置(${storageKey})失败:`, e);
    }

    // 需要等列表加载完成和DOM更新后再恢复滚动位置
    if (!loading.value && list.value.length > 0) {
        // 先重绘瀑布流
        nextTick(() => {
            $redrawVueMasonry(masonryId);
            console.log(`${masonryId}瀑布流重绘完成`);
            // 然后恢复滚动位置
            restoreScrollPosition();
        });
    } else {
        // 如果列表还在加载，等加载完成后再恢复位置
        watch(loading, (newVal) => {
            if (!newVal && list.value.length > 0) {
                // 先重绘瀑布流
                nextTick(() => {
                    $redrawVueMasonry(masonryId);
                    console.log(`${masonryId}瀑布流重绘完成`);
                    // 然后恢复滚动位置
                    restoreScrollPosition();
                });
            }
        }, { immediate: true, once: true });
    }
});

// 恢复滚动位置的函数
const restoreScrollPosition = () => {
    // 使用多次尝试的方式确保滚动位置能够被正确恢复
    const attemptRestore = (attempt = 0, maxAttempts = 3) => {
        if (attempt >= maxAttempts) return;

        nextTick(() => {
            try {
                const scrollContainer = getScrollContainer();

                if (!scrollContainer) {
                    console.error('未找到滚动容器，重试中...');
                    setTimeout(() => attemptRestore(attempt + 1, maxAttempts), 200);
                    return;
                }

                // 先触发重绘，然后再设置滚动位置
                if (isRefurbish.value && attempt === 0) {
                    console.log('触发瀑布流重绘');
                    $redrawVueMasonry();
                    // 重绘后需要等待DOM更新再设置滚动位置
                    setTimeout(() => {
                        if (scrollPosition.value > 0) {
                            console.log(`尝试恢复滚动位置(${attempt + 1}/${maxAttempts}):`, scrollPosition.value);
                            scrollContainer.scrollTop = scrollPosition.value;

                            // 检查是否成功设置滚动位置
                            if (Math.abs(scrollContainer.scrollTop - scrollPosition.value) > 10) {
                                // 如果差异较大，再次尝试
                                setTimeout(() => attemptRestore(attempt + 1, maxAttempts), 200);
                            } else {
                                console.log('滚动位置恢复成功!');
                                isRefurbish.value = false;
                            }
                        }
                    }, 100);
                } else if (scrollPosition.value > 0) {
                    console.log(`尝试恢复滚动位置(${attempt + 1}/${maxAttempts}):`, scrollPosition.value);
                    scrollContainer.scrollTop = scrollPosition.value;

                    // 检查是否成功设置滚动位置
                    if (Math.abs(scrollContainer.scrollTop - scrollPosition.value) > 10) {
                        // 如果差异较大，再次尝试
                        setTimeout(() => attemptRestore(attempt + 1, maxAttempts), 200);
                    } else {
                        console.log('滚动位置恢复成功!');
                    }
                }
            } catch (err) {
                console.error('恢复滚动位置失败:', err);
                // 失败后再次尝试
                setTimeout(() => attemptRestore(attempt + 1, maxAttempts), 200);
            }
        });
    };

    // 开始尝试恢复
    attemptRestore();
};

// 修改homeBack事件处理，确保在从举报页面返回时能正确恢复滚动位置
emitter.on('homeBack', () => {
    console.log('homeBack触发');
    isRefurbish.value = true;

    // 检查是从哪个页面返回的
    const referrer = document.referrer;
    console.log('返回来源:', referrer);

    // 根据isMatchType选择不同的键名
    const storageKey = props.isMatchType ? 'matchScrollPosition' : 'waterfallScrollPosition';

    // 确保从localStorage读取最新的滚动位置
    try {
        const savedPosition = localStorage.getItem(storageKey);
        if (savedPosition && !isNaN(Number(savedPosition))) {
            scrollPosition.value = Number(savedPosition);
            console.log(`从localStorage获取滚动位置(${storageKey}):`, scrollPosition.value);
        }
    } catch (e) {
        console.error(`从localStorage获取滚动位置(${storageKey})失败:`, e);
    }

    // 先延迟一点时间确保组件已经挂载
    setTimeout(() => {
        const scrollContainer = getScrollContainer();

        if (!scrollContainer) {
            console.error('未找到滚动容器，将在数据加载后重试');
            // 如果找不到滚动容器，等待一段时间后再次尝试
            setTimeout(() => restoreScrollPosition(), 500);
            return;
        }

        // 确保数据已加载完成再尝试恢复滚动位置
        if (!loading.value && list.value.length > 0) {
            console.log('立即尝试恢复滚动位置');
            restoreScrollPosition();
        } else {
            console.log('等待数据加载完成后恢复滚动位置');
            // 设置观察器监听内容变化
            const observer = new MutationObserver((mutations) => {
                if (!loading.value && list.value.length > 0) {
                    restoreScrollPosition();
                    observer.disconnect();
                }
            });

            observer.observe(scrollContainer, {
                childList: true,
                subtree: true
            });

            // 为防止观察器长时间不触发，设置一个超时
            setTimeout(() => {
                observer.disconnect();
                restoreScrollPosition();
            }, 2000);
        }
    }, 300);
});
// 在离开前保存滚动位置
onDeactivated(() => {
    console.log('onDeactivated离开')
});

onUnmounted(() => {
    // 移除滚动事件监听
    if (throttledSaveScroll.value) {
        const scrollContainer = getScrollContainer();

        if (scrollContainer) {
            scrollContainer.removeEventListener('scroll', throttledSaveScroll.value);
            console.log('已移除滚动事件监听');
        }
    }

    emitter.off('likeListSuccess')
    emitter.off('cancelCollects')
    emitter.off('updateWaterfallList')
    emitter.off('likeListSuccess')
    emitter.off('collectSuccess')
    emitter.off('homeBack')
    emitter.off('openAdd')
    emitter.off('getTabData')
    emitter.off('loginSuccess')
    emitter.off('imageLoaded')
})

// 添加一个函数用于重新添加滚动监听
const reattachScrollListener = () => {
    console.log('重新添加滚动监听');
    // 清空之前滚动的值
    const storageKey = props.isMatchType ? 'matchScrollPosition' : 'waterfallScrollPosition';
    if (localStorage.getItem(storageKey)) {
        localStorage.setItem(storageKey, 0);
    }
    // 先移除可能存在的滚动监听
    if (throttledSaveScroll.value) {
        const oldContainer = getScrollContainer();
        if (oldContainer) {
            oldContainer.removeEventListener('scroll', throttledSaveScroll.value);
            console.log('移除旧的滚动事件监听');
        }
    }

    // 等待DOM更新后添加新的滚动监听
    nextTick(() => {
        setTimeout(() => {
            const scrollContainer = getScrollContainer();

            if (scrollContainer) {
                throttledSaveScroll.value = _.throttle(() => {
                    // console.log('滚动事件触发，保存位置');
                    saveScrollPosition();
                }, 100); // 降低节流时间，提高响应速度

                scrollContainer.addEventListener('scroll', throttledSaveScroll.value);
                console.log('已添加新的滚动事件监听');
            } else {
                console.error('未找到滚动容器，无法添加滚动事件监听');
            }
        }, 300);
    });
};

// 在数据变化时更新需要加载的图片总数
watch(() => list.value, (newList) => {
  if (newList.length > 0) {
    // 计算列表中的图片总数（考虑到有些项目可能没有图片）
    totalImagesInView.value = newList.filter(item => 
      (item.imageList && item.imageList.length > 0) || item.videoFrame
    ).length;
    
    // 重置已加载图片计数
    loadedImagesCount.value = 0;
    console.log(`需要加载的图片总数: ${totalImagesInView.value}`);
  }
}, { deep: true });

</script>
<style scoped lang="scss">
.matchTypeCon {
    height: 100%;
}

.waterFallCon {
    flex: 1;
    overflow: auto;


}

.vanList {
    height: 100%;
    overflow: auto;

    .item {
        width: 48%;
        position: relative;
        margin-bottom: 18px;
        margin-left: 1%;
        margin-right: 1%;
        min-height: 80px;
        box-sizing: border-box;
    }

    .noComment {
        height: 100%;
    }

}

.affix {
    position: fixed;
    bottom: 25px;
    right: 42px;

    img {
        width: 129px;
        height: 129px;
    }
}

.match {
    position: absolute;
    bottom: 160px;
    right: 52px;

    img {
        width: 108px;
        height: 108px;
    }

}
</style>