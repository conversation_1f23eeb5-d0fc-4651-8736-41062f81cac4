<template>
    <!-- <div v-if="isLoading" class="local-loading">
        <van-loading color="#1989fa" size="60" />
    </div> -->
    <!-- v-else -->
    <div class="container">
        <Menu :type="dynamicType" @changeMenu=changeMenu></Menu>
        <div class="con">
            <div class="message" v-if="activeTab == 7">
                <MessageList :activeTab="activeTab"></MessageList>
            </div>
            <div class="vote" v-if="activeTab == 6">
                <VoteList></VoteList>
            </div>
            <div class="waterFall" v-show="activeTab < 6">
                <WaterfallList :activeTab="activeTab"></WaterfallList>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, onMounted, onBeforeMount, onActivated, onUnmounted, nextTick, defineAsyncComponent } from 'vue';
import { showToast } from 'vant';
defineOptions({ name: 'index' })
import WaterfallList from "@/components/index/waterfallList.vue"
const Menu = defineAsyncComponent(() =>
    import('@/components/menu.vue')
);
// const WaterfallList = defineAsyncComponent(() =>
//     import('@/components/index/waterfallList.vue')
// );
const MessageList = defineAsyncComponent(() =>
    import('@/components/message/messageList.vue')
);
const VoteList = defineAsyncComponent(() =>
    import('@/components/vote/voteList.vue')
);
import emitter from '@/utils/mitt.js'
import { login, user, friendData, language } from "@/api/home.js"
import useCounterStore from "@/stores/counter"
const countStore = useCounterStore()
import { useI18n } from "vue-i18n";
const { t } = useI18n();
import { getCurrentInstance } from 'vue';
const { appContext } = getCurrentInstance();
import { getDeviceType } from "@/utils/deviceType.js"
const activeTab = ref(0)
const dynamicType = ref('')
const type = ref('dynamic')
const isLoading = ref(true)
import { setI18nLanguage } from "@/locales/index.js"
onBeforeMount(() => {
    // 获取url传参
    getRequest()
})
// 获取 vue-i18n 实例的 locale 属性  
const { locale } = useI18n({ useScope: "global" });
const getRequest = () => {
    const url = window.top.location.href;
    const params = {};
    url.replace(/[?&]+([^=&]+)=([^&]*)/gi, function (m, key, value) {
        params[key] = decodeURIComponent(value);
    });
    console.log(params); // 输出包含查询参数键值对组成的对象
    //   roleId seeUserAvatarId
    if (params.roleId) {
        countStore.loginData.roleId = params.roleId
        countStore.seeUserAvatarId = params.seeUserAvatarId
        countStore.seeUserRoleId = params.seeUserRoleId
        countStore.loginData.token = params.token
        countStore.loginData.avatarId = params.avatarId
        countStore.language = params.language
        countStore.apiToken = params.apiToken
        loginBtn()
        // 改变默认语言
        setLanguage(countStore.language)
        // locale.value = countStore.language
        //判断登录id和要查看的id是否是同一个 roleId seeUserAvatarId
        if (params.seeUserAvatarId) {
            countStore.loginRole = 'other'
            getFriendData()
            activeTab.value = 1
            setTimeout(() => {
                ta.track('openOtherDynamic', { open_count: 1 });
            }, 2000); // 2秒后清除提示
        } else {
            console.log('自己')
            countStore.loginRole = 'oneself'
            activeTab.value = 5
            setTimeout(() => {
                ta.track('openMyDynamic', { open_count: 1 });
            }, 2000); // 2秒后清除提示
        }
        dynamicType.value = countStore.loginRole
    }
}
const app = appContext.app;
app.config.globalProperties.$openPermission = function (data) {
    console.log('这是一个全局方法' + data);
    if (data == 'true') {
        emitter.emit('openShowAudio', countStore.audioType, data)
    } else {
        showToast(t('add.audioPermission'));
    }
};
app.config.globalProperties.$openCameraPermission = function (data) {
    console.log('这是一个打开相机权限的方法' + data);
    if (data == 'true') {
        emitter.emit('isUploadImage', data)
        emitter.emit('isUploadVideo', data)
        countStore.isCamera = true
    } else {
        showToast(t('add.imagePermission'));
    }
}
app.config.globalProperties.$getCheckCameraPermission = function (data) {
    console.log('这是一个检查相机权限的方法' + data);
    // emitter.emit('cameraPermission', data)
    if (data == 'true') {
        countStore.isCamera = true
    } else {
        countStore.isCamera = false
    }

}
app.config.globalProperties.$getImageUrl = function (data) {
    console.log('是否有要上传的图片' + data);
    if (data) {
        // 打开add页面
        emitter.emit('openAdd', 's')
        countStore.baseUrl = 'data:image/png;base64,' + data
    }

};
// // 打开竖屏
// app.config.globalProperties.$getChangeToPortrait = function (data) {
//     console.log('竖屏打开' + data);
//     showToast(t('打开竖屏，跳转到详情页面'));
//     emitter.emit('openMatch')
//     //   
// }
// // 打开横屏
// app.config.globalProperties.$getChangeToLandscape = function (data) {
//     console.log('横屏打开' + data);
//     showToast(t('打开横屏，返回上一页'));
//     emitter.emit('backMatch')

// }
// 将方法挂载到 window 上，使其可以在全局范围内被调用(例如从 unity)
window.openPermission = app.config.globalProperties.$openPermission;
window.openCameraPermission = app.config.globalProperties.$openCameraPermission;
window.getCheckCameraPermission = app.config.globalProperties.$getCheckCameraPermission;
window.getImageUrl = app.config.globalProperties.$getImageUrl;
// window.getChangeToPortrait = app.config.globalProperties.$getChangeToPortrait;
// window.getChangeToLandscape = app.config.globalProperties.$getChangeToLandscape;
const setLanguage = (data) => {
    // 调用 setI18nLanguage 并在成功后执行后续操作
    setI18nLanguage(data)
        .then((lang) => {
            console.log('语言设置成功:', lang);
            // 像waterfallList传递获取tabs数据
            emitter.emit('getTabData')
        })
        .catch((error) => {
            console.error('语言设置失败:', error);
        });
}
const userInfo = () => {
    const userType = countStore.loginRole == 'oneself' ? 1 : 2
    const userId = countStore.seeUserRoleId ? countStore.seeUserRoleId : countStore.loginData.roleId
    user(userType, userId)
        .then((res) => {
            console.log(res)
            if (res.code === 200) {
                countStore.userData = res.data
                // 未读数存储
                if (res.data.noReadMessageNum == 'null') {
                } else {
                    countStore.noReadMessageNum = res.data.noReadMessageNum
                    countStore.likeAndcollectNum = res.data.likeAndcollectNum
                    countStore.aitAndConntentNum = res.data.aitAndConntentNum
                    countStore.replyNum = res.data.replyNum
                }

            } else if (res.code === 401) {
                // 清除缓存数据 再次登录
                localStorage.clear()
                loginBtn()
            } else {
                showToast(t('toast.getMsgFail'));
            }
        })
        .catch(function (error) {
            console.log(error);
            showToast(t('toast.getMsgFail'));
        });
};
const getLanguage = (() => {
    const id = countStore.loginData.roleId
    console.log(id)
    language(id)
        .then((res) => {
            // 接口调用成功之后的操作
            console.log(res)
            console.log(res.data)
            if (res.code === 200) {
                countStore.userLanguage = res.data
                console.log(countStore.userLanguage)
            } else {
                showToast(t('toast.getMsgFail'));
            }
        })
        .catch((err) => {
            // 接口调用失败之后的操作
            console.log(err)
            showToast(t('toast.getMsgFail'));
        })
})
const getFriendData = (() => {
    const query = {
        avatarId: countStore.seeUserAvatarId,
        roleId: countStore.seeUserRoleId,
    }
    friendData(query)
        .then((res) => {
            // 接口调用成功之后的操作
            console.log(res)
            if (res.code === 200) {
            } else {
                showToast(t('toast.loginFail'));
            }
        })
        .catch((err) => {
            // 接口调用失败之后的操作
            console.log(err)
            showToast(t('toast.loginFail'));
        })
})
const loginBtn = (() => {
    const query = {
        avatarId: countStore.loginData.avatarId,
        roleId: countStore.loginData.roleId,
        token: countStore.loginData.token,
        timestamp: countStore.loginData.timestamp
    }
    login(query)
        .then((res) => {
            // 接口调用成功之后的操作
            console.log(res)
            isLoading.value = false
            if (res.code === 200) {
                localStorage.setItem('token', res.token)
                userInfo()
                getLanguage()
            } else {
                showToast(t('toast.loginFail'));
            }
            emitter.emit('loginSuccess')
        })
        .catch((err) => {
            // 接口调用失败之后的操作
            isLoading.value = false
            console.log(err)
            showToast(t('toast.loginFail'));
        })

})
// tab栏改变
const changeMenu = (tab) => {
    console.log(tab)
    activeTab.value = tab
}
onMounted(() => {
    countStore.deviceType = getDeviceType()
    console.log(countStore.deviceType);
    if (countStore.deviceType == 'Web') {
    } else {
        window.location.href = "uniwebview://checkCameraPermission";
    }
    localStorage.setItem('homeScrollPosition', 0);
    // setTimeout(()=>{
    //     emitter.emit('openAdd', 's')
    //     countStore.baseUrl='s'
    // },3000)
})
// // 激活钩子函数
// onActivated(() => {
//     console.log('激活')
//     nextTick(() => {
//         $redrawVueMasonry()
//     })
// })
// 监听到屏蔽
emitter.on('blockDySuccess', () => {
    console.log('屏蔽')
    if (activeTab.value == 4 || activeTab.value == 5 || activeTab.value == 2 || activeTab.value == 3) {
        // 更新waterfall
        emitter.emit('updateWaterfallList')
    } else if (activeTab.value == 7) {
        // 更新message
        emitter.emit('updateMessageList')
    }
})
// 监听到删除动态
emitter.on('deleteDySuccess', (data) => {
    console.log(data)
    if (activeTab.value == 1 || activeTab.value == 4 || activeTab.value == 5) {
        // 更新waterfall
        emitter.emit('updateWaterfallList')
    } else if (activeTab.value == 7) {
        // 更新message
        emitter.emit('updateMessageList')
    }
})
//监听到强制刷新
emitter.on('refurbishList', () => {
    console.log('强制刷新')
    if (activeTab.value == 7) {
        // 更新message
        emitter.emit('updateMessageList')
    } else {
        // 更新waterfall
        emitter.emit('updateWaterfallList')
    }
})
onUnmounted(() => {
    emitter.off('deleteDySuccess')
    emitter.off('refurbishList')
    emitter.off('blockDySuccess')
})
</script>
<style scoped lang="scss">
.local-loading {
    height: 100vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.container {
    display: flex;

    .con {
        height: 100vh;
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .message {
            height: 100%;
            display: flex;
            flex-direction: column;
            background: var(--bgColor);
        }

        .waterFall {
            height: 100%;
            display: flex;
            flex-direction: column;
            background: var(--bgColor);
        }

        .vote {
            background-color: #BEDFFF;
            height: 100%;
        }

    }
}
</style>