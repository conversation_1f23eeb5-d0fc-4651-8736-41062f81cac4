/**
 * 瀑布流管理工具
 * 用于隔离不同页面/组件的瀑布流实例，避免全局冲突
 */

export class MasonryManager {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = null;
        this.masonryInstance = null;
    }

    /**
     * 初始化瀑布流容器
     * @param {HTMLElement} containerElement - 容器元素
     */
    init(containerElement) {
        this.container = containerElement;
        this.findMasonryInstance();
    }

    /**
     * 查找瀑布流实例
     */
    findMasonryInstance() {
        if (!this.container) return;
        
        const masonryElement = this.container.querySelector('[v-masonry]');
        if (masonryElement && masonryElement.__vue_masonry__) {
            this.masonryInstance = masonryElement.__vue_masonry__;
            console.log(`找到瀑布流实例: ${this.containerId}`);
        }
    }

    /**
     * 重绘瀑布流布局
     * @param {number} delay - 延迟时间
     */
    layout(delay = 0) {
        setTimeout(() => {
            if (this.masonryInstance) {
                console.log(`重绘瀑布流布局: ${this.containerId}`);
                this.masonryInstance.layout();
            } else {
                // 重新查找实例
                this.findMasonryInstance();
                if (this.masonryInstance) {
                    console.log(`重新找到并重绘瀑布流: ${this.containerId}`);
                    this.masonryInstance.layout();
                } else {
                    console.warn(`未找到瀑布流实例: ${this.containerId}`);
                }
            }
        }, delay);
    }

    /**
     * 销毁瀑布流管理器
     */
    destroy() {
        this.container = null;
        this.masonryInstance = null;
        console.log(`销毁瀑布流管理器: ${this.containerId}`);
    }
}

/**
 * 创建瀑布流管理器
 * @param {string} containerId - 容器ID
 * @returns {MasonryManager}
 */
export function createMasonryManager(containerId) {
    return new MasonryManager(containerId);
}

/**
 * 无感滚动位置恢复工具
 */
export class ScrollPositionManager {
    constructor(scrollContainer, storageKey) {
        this.scrollContainer = scrollContainer;
        this.storageKey = storageKey;
        this.savedPosition = 0;
    }

    /**
     * 保存滚动位置
     */
    savePosition() {
        if (!this.scrollContainer) return;
        
        this.savedPosition = this.scrollContainer.scrollTop;
        
        try {
            localStorage.setItem(this.storageKey, this.savedPosition);
            console.log(`保存滚动位置 ${this.storageKey}:`, this.savedPosition);
        } catch (e) {
            console.error('保存滚动位置失败:', e);
        }
    }

    /**
     * 恢复滚动位置
     */
    restorePosition() {
        try {
            const savedPosition = localStorage.getItem(this.storageKey);
            if (savedPosition && !isNaN(Number(savedPosition))) {
                this.savedPosition = Number(savedPosition);
            }
        } catch (e) {
            console.error('读取滚动位置失败:', e);
        }

        if (this.scrollContainer && this.savedPosition > 0) {
            console.log(`恢复滚动位置 ${this.storageKey}:`, this.savedPosition);
            this.scrollContainer.scrollTop = this.savedPosition;
            return true;
        }
        return false;
    }

    /**
     * 无感恢复滚动位置（先设置位置再重绘）
     * @param {MasonryManager} masonryManager - 瀑布流管理器
     * @param {Function} callback - 完成回调
     */
    restorePositionSeamlessly(masonryManager, callback) {
        if (!this.scrollContainer || this.savedPosition <= 0) {
            callback && callback();
            return;
        }

        console.log(`开始无感恢复滚动位置 ${this.storageKey}:`, this.savedPosition);

        // 先直接设置滚动位置，避免从顶部跳动
        this.scrollContainer.scrollTop = this.savedPosition;

        // 然后触发瀑布流重绘
        if (masonryManager) {
            masonryManager.layout(50);
        }

        // 重绘后再次确认位置
        setTimeout(() => {
            if (this.scrollContainer) {
                const currentPosition = this.scrollContainer.scrollTop;
                if (Math.abs(currentPosition - this.savedPosition) > 10) {
                    console.log('重绘后位置偏移，重新设置:', currentPosition, '->', this.savedPosition);
                    this.scrollContainer.scrollTop = this.savedPosition;
                }
                console.log(`无感滚动位置恢复完成 ${this.storageKey}`);
                callback && callback();
            }
        }, 150);
    }

    /**
     * 清除保存的位置
     */
    clearPosition() {
        this.savedPosition = 0;
        try {
            localStorage.removeItem(this.storageKey);
        } catch (e) {
            console.error('清除滚动位置失败:', e);
        }
    }
}
