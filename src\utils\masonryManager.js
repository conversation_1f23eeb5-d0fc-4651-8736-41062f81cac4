/**
 * 瀑布流管理工具
 * 用于隔离不同页面/组件的瀑布流实例，避免全局冲突
 */

export class MasonryManager {
    constructor(containerId) {
        this.containerId = containerId;
        this.container = null;
        this.masonryInstance = null;
    }

    /**
     * 初始化瀑布流容器
     * @param {HTMLElement} containerElement - 容器元素
     */
    init(containerElement) {
        this.container = containerElement;
        this.findMasonryInstance();
    }

    /**
     * 查找瀑布流实例
     */
    findMasonryInstance() {
        if (!this.container) return;
        
        const masonryElement = this.container.querySelector('[v-masonry]');
        if (masonryElement && masonryElement.__vue_masonry__) {
            this.masonryInstance = masonryElement.__vue_masonry__;
            console.log(`找到瀑布流实例: ${this.containerId}`);
        }
    }

    /**
     * 重绘瀑布流布局
     * @param {number} delay - 延迟时间
     */
    layout(delay = 0) {
        setTimeout(() => {
            if (this.masonryInstance) {
                console.log(`重绘瀑布流布局: ${this.containerId}`);
                // 先触发resize事件，确保容器尺寸正确
                window.dispatchEvent(new Event('resize'));
                // 然后重新布局
                this.masonryInstance.layout();
            } else {
                // 重新查找实例
                this.findMasonryInstance();
                if (this.masonryInstance) {
                    console.log(`重新找到并重绘瀑布流: ${this.containerId}`);
                    window.dispatchEvent(new Event('resize'));
                    this.masonryInstance.layout();
                } else {
                    console.warn(`未找到瀑布流实例: ${this.containerId}`);
                    // 备用方案：触发全局resize
                    window.dispatchEvent(new Event('resize'));
                }
            }
        }, delay);
    }

    /**
     * 强制重建瀑布流布局（用于页面切换时）
     */
    forceRebuild() {
        console.log(`强制重建瀑布流布局: ${this.containerId}`);

        // 先触发resize确保容器尺寸正确
        window.dispatchEvent(new Event('resize'));

        // 重新查找实例
        this.findMasonryInstance();

        if (this.masonryInstance) {
            // 销毁现有布局
            if (this.masonryInstance.destroy) {
                this.masonryInstance.destroy();
            }

            // 重新初始化
            setTimeout(() => {
                this.findMasonryInstance();
                if (this.masonryInstance) {
                    this.masonryInstance.layout();
                    console.log(`强制重建完成: ${this.containerId}`);
                }
            }, 100);
        }
    }

    /**
     * 销毁瀑布流管理器
     */
    destroy() {
        this.container = null;
        this.masonryInstance = null;
        console.log(`销毁瀑布流管理器: ${this.containerId}`);
    }
}

/**
 * 创建瀑布流管理器
 * @param {string} containerId - 容器ID
 * @returns {MasonryManager}
 */
export function createMasonryManager(containerId) {
    return new MasonryManager(containerId);
}

/**
 * 无感滚动位置恢复工具
 */
export class ScrollPositionManager {
    constructor(scrollContainer, storageKey) {
        this.scrollContainer = scrollContainer;
        this.storageKey = storageKey;
        this.savedPosition = 0;
    }

    /**
     * 保存滚动位置
     */
    savePosition() {
        if (!this.scrollContainer) return;
        
        this.savedPosition = this.scrollContainer.scrollTop;
        
        try {
            localStorage.setItem(this.storageKey, this.savedPosition);
            console.log(`保存滚动位置 ${this.storageKey}:`, this.savedPosition);
        } catch (e) {
            console.error('保存滚动位置失败:', e);
        }
    }

    /**
     * 恢复滚动位置
     */
    restorePosition() {
        try {
            const savedPosition = localStorage.getItem(this.storageKey);
            if (savedPosition && !isNaN(Number(savedPosition))) {
                this.savedPosition = Number(savedPosition);
            }
        } catch (e) {
            console.error('读取滚动位置失败:', e);
        }

        if (this.scrollContainer && this.savedPosition > 0) {
            console.log(`恢复滚动位置 ${this.storageKey}:`, this.savedPosition);
            this.scrollContainer.scrollTop = this.savedPosition;
            return true;
        }
        return false;
    }

    /**
     * 无感恢复滚动位置（优化版本）
     * @param {MasonryManager} masonryManager - 瀑布流管理器
     * @param {Function} callback - 完成回调
     */
    restorePositionSeamlessly(masonryManager, callback) {
        // 先从localStorage读取最新位置
        try {
            const savedPosition = localStorage.getItem(this.storageKey);
            if (savedPosition && !isNaN(Number(savedPosition))) {
                this.savedPosition = Number(savedPosition);
            }
        } catch (e) {
            console.error('读取滚动位置失败:', e);
        }

        if (!this.scrollContainer || this.savedPosition <= 0) {
            // 如果没有保存的位置，仍然需要重绘确保布局正确
            if (masonryManager) {
                masonryManager.layout(100);
            }
            callback && callback();
            return;
        }

        console.log(`开始无感恢复滚动位置 ${this.storageKey}:`, this.savedPosition);

        // 先触发瀑布流重绘，确保布局正确
        if (masonryManager) {
            masonryManager.layout(0);
        }

        // 等待重绘完成后再设置滚动位置
        setTimeout(() => {
            if (this.scrollContainer) {
                this.scrollContainer.scrollTop = this.savedPosition;
                console.log('设置滚动位置:', this.savedPosition);

                // 再次确认位置并进行微调
                setTimeout(() => {
                    if (this.scrollContainer) {
                        const currentPosition = this.scrollContainer.scrollTop;
                        if (Math.abs(currentPosition - this.savedPosition) > 10) {
                            console.log('位置偏移，重新设置:', currentPosition, '->', this.savedPosition);
                            this.scrollContainer.scrollTop = this.savedPosition;
                        }
                        console.log(`无感滚动位置恢复完成 ${this.storageKey}`);
                        callback && callback();
                    }
                }, 100);
            }
        }, 200);
    }

    /**
     * 清除保存的位置
     */
    clearPosition() {
        this.savedPosition = 0;
        try {
            localStorage.removeItem(this.storageKey);
        } catch (e) {
            console.error('清除滚动位置失败:', e);
        }
    }
}
