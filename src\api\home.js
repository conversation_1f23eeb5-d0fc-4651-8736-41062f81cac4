import request from "@/axios/axios";
import axios from 'axios';
let cancelToken = axios.CancelToken;
let cancel;
let cancelTokens = [];
// 登录
export function login(data) {
    return request({
        url: '/game/login',
        method: 'POST',
        data
    })
}
// 查询角色基础信息
export function user(userType,userId) {
    return request({
        url: `/game/user/base/${userType}/${userId}`,
        method: 'GET',
    })
}
// 查询角色好友关系
export function friendShip(params) {
    return request({
        url: '/game/user/friendShip',
        method: 'GET',
        params
    })
}
// 分页查询动态列表
export function getDynamicList(params) {
    const CancelToken = axios.CancelToken;
    const source = CancelToken.source();
    cancelTokens.push(source.cancel);
    return request({
        url:'/game/dynamics/list',
        method: 'GET',
        params,
        cancelToken: source.token,
    })
}
export function axiosCancel() {
    while (cancelTokens.length > 0) {
        cancelTokens.pop()();
    }
  }
// 分页查询消息列表
export function getmsgList(params) {
    return request({
        url:'/game/dynamics/noRead/list',
        method: 'GET',
        params
    })
}
// 获取动态详细信息
export function getDynamicDetail(id) {
    return request({
        url:`/game/dynamics/${id}`,
        method: 'GET',
    })
}
// 查询评论列表
export function getComment(params) {
    return request({
        url:'/game/comment/list',
        method: 'GET',
        params
    })
}
// 添加评论
export function commentAdd(data) {
    return request({
        url: '/game/comment',
        method: 'POST',
        data
    })
}
// 删除评论
export function commentDelete(id) {
    return request({
        url: `/game/comment/${id}`,
        method: 'DELETE',
    })
}
// 查询回复列表
export function getReply(params) {
    return request({
        url:'/game/reply/list',
        method: 'GET',
        params
    })
}
// 添加回复
export function replyAdd(data) {
    return request({
        url: '/game/reply',
        method: 'POST',
        data
    })
}
// 删除回复
export function replyDelete(id) {
    return request({
        url: `/game/reply/${id}`,
        method: 'DELETE',
    })
}
// 添加动态
export function add(data) {
    return request({
        url: '/game/dynamics',
        method: 'POST',
        data
    })
}
// 修改动态
export function modify(data) {
    return request({
        url: '/game/dynamics',
        method: 'PUT',
        data
    })
}
//删除动态
export function deleteDynamic(id) {
    return request({
        url:`/game/dynamics/${id}`,
        method: 'DELETE',

    })
}
// 点赞
export function like(data) {
    return request({
        url: '/game/like',
        method: 'POST',
        data
    })
}
// 收藏
export function collect(data) {
    return request({
        url: '/game/collect',
        method: 'POST',
        data
    })
}
// 上传图片
export function uploadImage(data) {
    return request({
        url: '/game/common/upload',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        method: 'POST',
        data
    })
}
export function uploadVideo(data) {
    return request({
        url: '/game/common/video/upload',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        method: 'POST',
        data
    })
}
// 上传文件
export function uploadFile(data) {
    return request({
        url: '/game/common/file/upload',
        headers: {
            'Content-Type': 'multipart/form-data'
        },
        method: 'POST',
        data
    })
}
// 举报
export function report(data) {
    return request({
        url: '/game/complain/front',
        method: 'POST',
        data
    })
}
// 查看其他人
export function friendData(data) {
    return request({
        url: '/game/friend/login',
        method: 'POST',
        data
    })
}
export function getAvatarld(id) {
    return request({
        url: `/game/getAvatarId/${id}`,
        method: 'GET',
    })
}
// 屏蔽
export function getBlock(id) {
    return request({
        url: `/game/dynamics/shield/${id}`,
        method: 'GET',
    })
}
// 猜猜猜列表
export function voteList(data) {
    return request({
        url: '/system/vote/front/list',
        method: 'POST',
        data
    })
}
// 获取金币
export function coin(data) {
    return request({
        url: `/system/vote/front/getCoin`,
        method: 'POST',
        data
    })
}
// 投票
export function betting(data) {
    return request({
        url: '/system/vote/front/betting',
        method: 'POST',
        data
    })
}
// 查询角色语言信息
export function language(id) {
    return request({
        url: `/game/user/language/${id}`,
        method: 'GET',
    })
}
// 获取投票活动状态
export function preBetting(id) {
    return request({
        url: `/system/vote/front/preBetting/${id}`,
        method: 'PUT',
    })
}