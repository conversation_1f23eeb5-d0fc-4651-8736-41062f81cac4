// 导入你的本地化消息文件
import { createI18n } from "vue-i18n";
import {i18nType} from "@/utils/i18n.js"
// 创建 i18n 实例
const i18n = createI18n({
    locale: "English", // 设置默认语言
    globalInjection: true, // 全局注入,可以直接使用$t
    // fallbackLocale: "English", // 当没有指定语言时的回退语言
    messages: {
    },  // 初始为空，按需加载
    silentFallbackWarn:true,
    missingWarn:false,
    silentTranslationWarn:true,
    fallbackWarn:false,
    legacy: false,
  });
  const loadedLanguages = new Set(['English']);
  export async function setI18nLanguage(lang) {
    console.log(lang)
    try {
      const type=i18nType(lang)
      if(lang=='English'){
        var messages = await import('@locales/en_us.js');
      }else if (lang=='ChineseSimplified'){
        var messages = await import('@locales/zh_cn.js');
      }else if (lang=='Korean'){
        var messages = await import('@locales/ko.js');
      }else if (lang=='Japanese'){
        var messages = await import('@locales/jpn.js');
      }else if (lang=='German'){
        var messages = await import('@locales/ge.js');
      }else if (lang=='Spanish'){
        var messages = await import('@locales/sp.js');
      }else if (lang=='Italian'){
        var messages = await import('@locales/it.js');
      }else if (lang=='French'){
        var messages = await import('@locales/fr.js');
      }else if (lang=='Portuguese'){
        var messages = await import('@locales/pt.js');
      }else if (lang=='Russian'){
        var messages = await import('@locales/ru.js');
      }else if (lang=='Dutch'){
        var messages = await import('@locales/nl.js');
      }else if (lang=='Hindi'){
        var messages = await import('@locales/hi.js');
      }else if (lang=='Bengali'){
        var messages = await import('@locales/bn.js');
      }else if (lang=='Hungarian'){
        var messages = await import('@locales/hu.js');
      }else if (lang=='Turkish'){
        var messages = await import('@locales/tr.js');
      }else if (lang=='Vietnamese'){
        var messages = await import('@locales/vi.js');
      }else if (lang=='Indonesian'){
        var messages = await import('@locales/id.js');
      }
      i18n.global.setLocaleMessage(lang, messages.default);
      loadedLanguages.add(lang);
     
    } catch (error) {
      console.error('加载失败:', error);
      return Promise.reject(error);
    }
    i18n.global.locale.value = lang;
    document.documentElement.setAttribute('lang', lang);
    return lang;
  }
  setI18nLanguage('English')
  export default i18n;