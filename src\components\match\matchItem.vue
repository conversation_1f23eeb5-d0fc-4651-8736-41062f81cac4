<template>
    <div class="item" @click="handleClick">
        <img class="coverImage"
            :src="item.coverImage"
            alt="">
        <div class="card-info">
            <div class="info-title">{{ item.title }}</div>
            <div class="info-con">
                <div class="info-date">{{ $t('match.matchTime') }}: {{ item.signTime }}</div>
                <div class="info-icon" :class="{
                    'status-waiting': status === 0,
                    'status-progress': status === 2,
                    'status-finished': status === 3
                }">
                    {{ status === 0 ? $t('match.statusWaiting') : 
                       status === 2 ? $t('match.statusProgress') : 
                       $t('match.statusEnd') }}
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
// 接收父组件传递的 props
const props = defineProps({
    item: {
        type: Object,
        required: true
    },
    status: {
        type: Number,
        default: 2 // 默认进行中状态
    }
});

// 定义向父组件发出的事件
const emit = defineEmits(['item-click']);

// 处理点击事件
const handleClick = () => {
    // 向父组件发出点击事件，传递当前项目
    emit('item-click', props.item);
};
</script>

<style lang="scss" scoped>
// 视频卡片样式
.item {
    width: calc(50% - 8px) !important; // 强制设置宽度为50%减去间距的一半
    position: relative;
    min-height: 80px;
    box-sizing: border-box;
    margin: 0 4px 16px 4px !important; // 统一设置左右margin为4px，底部16px
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    overflow: hidden; // 确保内容不溢出
    transform: translateZ(0); // 为每个item启用硬件加速
    will-change: transform; // 优化变换性能

    .coverImage {
        width: 100%;
        height: 347px;
        object-fit: cover; // 确保图片填满容器
    }
}

// 卡片底部信息
.card-info {
    padding: 15px 0;

    .info-title {
        padding-left: 24px;
        font-size: 28px;
        color: rgba(51, 51, 51, 0.9);
        margin-bottom: 4px;
    }

    .info-con {
        display: flex;
        padding-left: 24px;

        .info-date {
            flex: 1;
            font-size: 26px;
            color: rgba(51, 51, 51, 0.68);
        }

        .info-icon {
            width: 120px;
            height: 44px;
            background-size: 100% 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #FFFFFF;
            
            &.status-waiting {
                background-image: url('@/assets/images/match/waiting.webp');
            }
            
            &.status-progress {
                background-image: url('@/assets/images/match/progress.webp');
            }
            
            &.status-finished {
                background-image: url('@/assets/images/match/end.webp');
            }
        }
    }
}
</style>
