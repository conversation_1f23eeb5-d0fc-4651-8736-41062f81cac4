<template>
    <div class="item">
        <div class="text-title">
            <span>{{ title }}</span>
        </div>
        <div class="text-content" v-html="data">
            
        </div>
    </div>
</template>
<script setup>
const props = defineProps({
    title: {
        type: String,
        default: ''
    },
    data: {
        type: String,
        default: ''
    }
})

</script>
<style lang="scss" scoped>
.item {
    width: 100%;
    height: 100%;

    .text-title {
        text-align: center;
        font-size: 42px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 20px;
        width: 100%;
    }

    .text-content {
        background: #FFFFFF;
        border-radius: 4px;
        padding: 42px 62px;
    }
}
</style>
