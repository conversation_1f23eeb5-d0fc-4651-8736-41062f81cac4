{"name": "proj-dynamic-h5", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite --mode development", "test": "vite build --mode test", "build": "vite build --mode production", "pre": "vite build --mode pre", "preview": "vite preview"}, "dependencies": {"@vant/touch-emulator": "^1.4.0", "acorn": "^8.0.0", "axios": "^1.7.9", "compressorjs": "^1.2.1", "lib-flexible": "^0.3.2", "lodash": "^4.17.21", "mitt": "^3.0.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.2.0", "postcss": "^8.0.0", "postcss-load-config": "^4.0.0", "quill": "^2.0.2", "recorder-core": "^1.3.24040900", "swiper": "^11.2.6", "thinkingdata-browser": "^2.1.0", "v-click-outside": "^3.2.0", "v3-click-outside": "^1.1.0", "vant": "^4.9.5", "vue": "^3.4.29", "vue-demi": "^0.14.6", "vue-i18n": "^10.0.0", "vue-masonry": "^0.16.0", "vue-router": "^4.3.3"}, "devDependencies": {"@vant/auto-import-resolver": "^1.3.0", "@vitejs/plugin-vue": "^5.0.5", "imagemin-webp": "^8.0.0", "postcss-px-to-viewport": "^1.1.1", "sass": "^1.78.0", "unplugin-auto-import": "^19.1.1", "unplugin-vue-components": "^28.4.1", "video.js": "^8.21.0", "vite": "^5.3.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1"}, "resolutions": {"postcss": "7.0.32"}}