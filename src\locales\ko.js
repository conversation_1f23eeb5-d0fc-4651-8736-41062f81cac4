const ko = {
    index:{
        title:'홈페이지 게시물',
        menuItems: {
            Dynamic: '게시물',
            Favorites: '수집',
            Liked: '좋아요',
            Messages: '메시지',
            weChat:'모멘트',
            myDynamic:'내 게시물',
            myFavorites:'내 즐겨찾기',
            myLiked:'내 좋아요',
            Hot:'인기 급상승',
            Recommend:'추천',
            New:'최신',
            Vote:'추측&획득',
        },
        user:{
            praised:'좋아요 얻기',
            collect:'수집',
            chat:'채팅',
            addFriends:'친구 추가',
            friendRequest:'친구 신청',
            verificationMessage:'인증 정보',
            sendNow:'즉시 보내기',
            verificationMessagePlaceholder:'안녕하세요! 만나서 반가워요',
        },
        msg:{
            likeCollectTab:'좋아요 및 즐겨찾기',
            commentAitTab:"댓글 및{'@'}",
            reply:'답장',
            likeCond:'당신의 게시물이 마음에 들었습니다',
            likeConc:'당신의 댓글을 좋아합니다',
            likeConr:'당신의 답장을 좋아합니다 ',
            collectCon:'당신의 게시물을 저장했습니다',
            commentCon:'당신의 게시물에 댓글을 달았습니다',
            replyCon:'답글',
            aitCon:"{'@'}당신"
        }
    },
    detail:{
      replyNum:'총 %num% 개의 답글이 있습니다',
      seeMore:'더 보기',
      stop:'접기',
      originalText:'원본 텍스트',
      translation:'번역',
      commentNum:'개의 댓글',
      send:'보내기',
      reply:'답장',
      replied:'답글을 달았습니다',
      likeListTitle:'%num% 명의 친구로부터 좋아요'
    },
    add:{
       title:'게시물',
       input:'게시물 내용을 입력하세요',
       topic:'주제 추가',
       whoSee:{
          title:'누구에게 보여줄 수 있나요',
          all:'모두',
          friend:'친구가 볼 수 있음',
          oneself:'자신만 볼 수 있음',
       },
       publish:'출시',
       audioStart:'말하기를 클릭하세요',
       audioEnd:'종료하려면 클릭하세요',
       searchPlaceholder:'친구를 검색하세요',
       confirm:'확인',
       audioPermission:'먼저 마이크 권한을 열어주세요',
       imagePermission:'먼저 카메라 권한을 활성화해주세요',
       aitUser:"{'@'}사용자"
    },
    report:{
       next:'다음 단계',
       confirmReport:'신고를 확인하세요',
       placeholder:'내용을 입력하세요',
       detailTitle:'상세 설명',
       imgTitle:'이미지 증거',
       reportSuccess:'신고 성공',
       reportFail:'신고 실패',
       reportSuccessInfo:'제출 후, 플랫폼은 적극적으로 검증하고 처리할 것입니다; 질서를 유지해주셔서 감사드립니다!',
       reportPublish:'반환 동작',
       reportOneTitleAfterD:'당신은 %name% 의 게시물을 보고하고 있습니다',
       reportOneTitleAfterC:'당신은 %name% 의 댓글 보고하고 있습니다',
       reportOneTitleAfterR:'당신은 %name% 답변을 보고하고 있습니다',
       reportTwoTitle:'%title%를 선택했습니다',
       reportEndTitleAfterD:'%name%의 게시물에 대한 보고서는 %title%에 속합니다',
       reportEndTitleAfterC:'%name%에 대한 댓글 보고서는 %title%에 속합니다',
       reportEndTitleAfterR:'%name%에 대한 답변 보고서는 %title%에 속합니다',
    },
    tooltip:{
        delete:'삭제',
        modify:'수정',
        cancelCollect:'즐겨찾기 해제',
        report:'보고',
        block:'차단',
    },
    delete:{
      deleteCon:'이 콘텐츠를 삭제하시겠습니까?',
      deleteCancel:'취소',
      deleteConfirm:'확인',
      blockCon:'정말 차단하시겠습니까?',
    },
    toast:{
        likeSuccess:'좋아요 성공',
        likeCancel:'좋아요가 취소되었습니다',
        likeFail:'좋아요 실패',
        collectSuccess:'좋아요 성공',
        collectCancel:'좋아요 취소됨',
        collectFail:'저장 실패',
        publishSuccess:'게시무을 성공적으로 발표됨',
        publishFail:'게시물 발표 실패하셨습니다',
        modifySuccess:'수정 성공',
        topicInfo:'최대 5개의 주제를 선택할 수 있습니다',
        aitInfo:"최대{'@'}5명의 사용자",
        ait:"{'@'}앞에 최소 1자를 입력하세요",
        dynamicInput:'동적 콘텐츠, 이미지, 비디오 중에서 최소 하나를 업로드해 주세요',
        nextInfo:'먼저 하나의 옵션을 선택하세요',
        reportSuccess:'신고 성공',
        reportFail:'신고 실패',
        audioTextSuccess:'음성을 텍스트로 변환하는 데 성공했습니다',
        audioTextFail:'음성 인식 실패',
        translationSuccess:'번역 성공',
        translationFail:'번역 실패',
        uploadImageFail:'업로드 실패',
        deleteSuccess:'삭제 성공',
        deleteFail:'삭제 실패',
        // 新加
        imageLimit:'파일 크기는 %num%MB를 초과할 수 없습니다',
        imageNum:'최대 9개의 이미지를 업로드할 수 있습니다',
        uploadPrompt:'사진 & 동영상 업로드를 클릭해주세요',
        filePrompt:'(Doc, docx 및 pdf 파일 형식을 지원하며 파일 크기가 5mb를 초과할 수 없음)',
        imageBefore:'단일 이미지는 4mb를 초과하지 않습니다',
        imageShowToast:'업로드 파일이 너무 큽니다',
        audioFail:'녹음 종료 오류',
        collectCancelFail:'취소 실패',
        collectCancelSuccess:'취소 성공',
        dynamicFail:'게시물이 존재하지 않습니다',
        addCommentViolation:'제출하신 내용이 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다. 수정 후 다시 제출해 주십시오.',
        addCommentFail:'댓글 추가에 실패했습니다',
        addReplyFail:'답글 추가에 실패했습니다',
        addDynamicViolation:'제출하신 "게시물" 내용이 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다.',
        addTopicViolation:'제출하신 "주제"의 내용이 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다.',
        addImageViolation:'제출하신 "이미지" 콘텐츠가 규정을 위반한 것으로 의심됩니다. 수정 후 다시 제출해 주시기 바랍니다.',
        topicCon:'주제 내용은 비어 있을 수 없습니다',
        getMsgFail:'정보를 가져오는 데 실패했습니다',
        loginFail:'로그인에 실패했습니다',
        aitInfoPermission:'현재 자신만 볼 수 있습니다',
        alreadyReport:'여러 번 신고하셨습니다. 플랫폼 피드백을 기다려 주십시오',
        commentAfterDelete:'의견이 삭제되었습니다',
        replyAfterDelete:'회신이 삭제되었습니다',
        msgDataListFail:'데이터 가져오기 실패',
        videoLimit:'동영상은 25MB를 초과할 수 없습니다.',
        videoPrompt:'최대 1개의 동영상을 업로드할 수 있습니다.',
        videoToast:'이미지 또는 동영상만 업로드 가능합니다',
        imageTitle:'이미지 업로드',
        videoTitle:'동영상 업로드',
        applySuccess:'신청 보내기 성공',
        applyFail:'신청 보내기 실패',
        blacklistPrompt:'블랙리스트에서는 친구를 추가할 수 없습니다',
        friendNumPrompt:'상대방의 친구 수가 가득 찼습니다',
        myNumPrompt:'현재 친구 수가 가득 찼습니다',
        failedPrompt:'파라미터 오류',
        alignPrompt:'이미 상대방을 친구로 추가했으므로 다시 신청할 수 없습니다',
        applyMyPrompt:'자신을 추가할 수 없습니다',
        alignApply:'이미 친구 요청을 보냈습니다. 48시간 후에 다시 요청할 수 있습니다',
        blockSuccess:'사용자가 블랙리스트에 추가되었습니다',
        blockFail:'차단에 실패했습니다',
        blockListFull:'차단 목록이 가득 찼습니다',
        checkAgreementPrompt:'귀하가《컨텐츠 게시 선언 》에 동의하지 않아 동태를 게시할 수 없습니다',
        AgreementFile:'해당 문서를 읽고 동의했습니다',
        fileTitle:'《컨텐츠 게시 선언 》',
        sameLanguagePrompt:'현재 같은 언어를 사용 중이므로 번역할 필요가 없습니다',

    },
    vote:{
        voteProgress:'진행 중',
        voteEnd:'종료되었습니다',
        voteSettle:'정산 완료',
        oneselfNum:'투표 완료',
        voteNum:'{num} 코인',
        timeName:'남은 시간',
        allNum:'총 코인 수',
        participateInVoting:'총 플레이어 수:',
        getCoins:'이번에 {num} 코인을 획득하셨습니다',
        voteBtn:'선택',
        voteTitle:'{num}에 투표하기',
        inputInfo:'수량을 선택해 주세요',
        voteConfirm:'확인',
        voteSuccess:'성공',
        voteFail:'실패',
        statusEnd:'이벤트가 종료되었습니다',
        voteTnfo:'이벤트에 참여하기 위해 필요한 최소 코인 수는 1입니다',
        hold:'보유',
        balance:'현재 계정 잔액이 부족합니다. 빠른 시일 내에 충전해 주세요',
        remainingTimeData:'{days}일 {hours}시 {minutes}분 {seconds}초',
        questionInfo:'사용자의 모든 코인은 이번 이벤트의 상금 풀로 들어가며, 정답을 맞춘 사용자는 그들이 추측한 코인의 수에 따라 상금 풀 내의 모든 【타임-스페이스 코인】을 나누게 됩니다.',
    },
    video:{
        videoIndex:'뭔가 말해보세요...',
        videoDetail:'상세 페이지',
        videoTitle:'비디오',
    },
    empty:{
        comment:'아직 댓글이 없습니다',
        list:'사용 가능한 데이터가 없습니다',
        content:'사용 가능한 콘텐츠가 없습니다',
        message:'메시지가 없습니다'
    }
}

export default ko;

